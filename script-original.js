// Hourly Rate Application - Exact Replica with Modern UI
class HourlyRateApp {
    constructor() {
        this.rates = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.selectedRates = new Set();
        this.totalRecords = 0;
        this.currentView = 'list'; // Default view mode

        this.init();
        this.loadSampleData();
    }

    init() {
        this.bindEvents();
        this.loadFromStorage();
        this.render();
    }

    bindEvents() {
        // Toolbar buttons
        document.getElementById('addBtn').addEventListener('click', () => this.openAddModal());
        document.getElementById('deleteBtn').addEventListener('click', () => this.deleteSelected());
        document.getElementById('saveBtn').addEventListener('click', () => this.saveData());
        document.getElementById('refreshBtn').addEventListener('click', () => this.refresh());

        // View mode buttons
        document.getElementById('listViewBtn').addEventListener('click', () => this.setViewMode('list'));
        document.getElementById('gridViewBtn').addEventListener('click', () => this.setViewMode('grid'));
        document.getElementById('cardViewBtn').addEventListener('click', () => this.setViewMode('card'));

        // Pagination
        document.getElementById('firstPageBtn').addEventListener('click', () => this.goToPage(1));
        document.getElementById('prevPageBtn').addEventListener('click', () => this.goToPage(this.currentPage - 1));
        document.getElementById('nextPageBtn').addEventListener('click', () => this.goToPage(this.currentPage + 1));
        document.getElementById('lastPageBtn').addEventListener('click', () => this.goToPage(this.getTotalPages()));
        document.getElementById('pageSize').addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 1;
            this.render();
        });

        // Modal events
        document.getElementById('modalCloseBtn').addEventListener('click', () => this.closeModal());
        document.getElementById('modalCancelBtn').addEventListener('click', () => this.closeModal());
        document.getElementById('modalOverlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) this.closeModal();
        });

        // Form submission
        document.getElementById('rateForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveRate();
        });

        // Confirmation modal
        document.getElementById('confirmCancelBtn').addEventListener('click', () => this.closeConfirmModal());
        document.getElementById('confirmModalOverlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) this.closeConfirmModal();
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
                this.closeConfirmModal();
            }
        });
    }

    loadSampleData() {
        // Load sample data exactly matching the original screenshot
        if (this.rates.length === 0) {
            this.rates = [
                { id: 1, rate: 125.00, effectiveFrom: '2025-11-29' },
                { id: 2, rate: 125.00, effectiveFrom: '2025-11-29' },
                { id: 3, rate: 125.00, effectiveFrom: '2025-11-29' },
                { id: 4, rate: 127.00, effectiveFrom: '2025-11-29' },
                { id: 5, rate: 128.00, effectiveFrom: '2025-11-29' },
                { id: 6, rate: 130.00, effectiveFrom: '2025-01-01' },
                { id: 7, rate: 132.00, effectiveFrom: '2025-01-01' },
                { id: 8, rate: 133.00, effectiveFrom: '2025-11-29' },
                { id: 9, rate: 133.00, effectiveFrom: '2025-11-29' },
                { id: 10, rate: 133.00, effectiveFrom: '2025-01-01' },
                // Additional records to reach 74 total as shown in original
                { id: 11, rate: 135.00, effectiveFrom: '2025-01-01' },
                { id: 12, rate: 140.00, effectiveFrom: '2025-02-01' },
                { id: 13, rate: 145.00, effectiveFrom: '2025-03-01' },
                { id: 14, rate: 150.00, effectiveFrom: '2025-04-01' },
                { id: 15, rate: 155.00, effectiveFrom: '2025-05-01' },
                { id: 16, rate: 160.00, effectiveFrom: '2025-06-01' },
                { id: 17, rate: 165.00, effectiveFrom: '2025-07-01' },
                { id: 18, rate: 170.00, effectiveFrom: '2025-08-01' },
                { id: 19, rate: 175.00, effectiveFrom: '2025-09-01' },
                { id: 20, rate: 180.00, effectiveFrom: '2025-10-01' }
            ];

            // Generate additional records to reach 74 total
            for (let i = 21; i <= 74; i++) {
                const baseRate = 125 + (i % 20) * 2.5;
                const monthOffset = (i % 12) + 1;
                this.rates.push({
                    id: i,
                    rate: parseFloat(baseRate.toFixed(2)),
                    effectiveFrom: `2025-${monthOffset.toString().padStart(2, '0')}-01`
                });
            }

            this.totalRecords = this.rates.length;
            this.saveToStorage();
        }
    }

    getPaginatedRates() {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        return this.rates.slice(startIndex, endIndex);
    }

    getTotalPages() {
        return Math.ceil(this.rates.length / this.pageSize);
    }

    render() {
        this.renderCurrentView();
        this.renderPagination();
        this.updateToolbar();
    }

    // View Mode Management
    setViewMode(viewMode) {
        this.currentView = viewMode;
        this.updateViewModeButtons();
        this.switchViewContainer();
        this.renderCurrentView();
        this.saveViewModeToStorage();
    }

    updateViewModeButtons() {
        // Remove active class from all buttons
        document.querySelectorAll('.btn-view-mode').forEach(btn => {
            btn.classList.remove('active');
        });

        // Add active class to current view button
        const activeBtn = document.querySelector(`[data-view="${this.currentView}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
    }

    switchViewContainer() {
        // Hide all view containers
        document.querySelectorAll('.view-container').forEach(container => {
            container.classList.remove('active');
        });

        // Show current view container
        const currentContainer = document.getElementById(`${this.currentView}View`);
        if (currentContainer) {
            currentContainer.classList.add('active');
        }
    }

    renderCurrentView() {
        switch (this.currentView) {
            case 'list':
                this.renderTable();
                break;
            case 'grid':
                this.renderGrid();
                break;
            case 'card':
                this.renderCards();
                break;
            default:
                this.renderTable();
        }
    }

    renderTable() {
        const tableBody = document.getElementById('tableBody');
        const paginatedRates = this.getPaginatedRates();

        if (paginatedRates.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="empty-state">
                        <i class="fas fa-clock"></i>
                        <h3>No hourly rates found</h3>
                        <p>Click "Add" to create your first hourly rate.</p>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = paginatedRates.map(rate => this.createTableRow(rate)).join('');
        this.bindTableEvents();
    }

    createTableRow(rate) {
        const isSelected = this.selectedRates.has(rate.id);
        return `
            <tr class="${isSelected ? 'selected' : ''}" data-id="${rate.id}">
                <td class="col-edit">
                    <button class="btn btn-action btn-edit edit-btn" title="Edit rate" data-id="${rate.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
                <td class="col-delete checkbox-cell">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} data-id="${rate.id}"
                           aria-label="Select rate for deletion">
                </td>
                <td class="col-rate">
                    <span class="rate-value">${rate.rate.toFixed(2)}</span>
                </td>
                <td class="col-date">
                    <span class="date-value">${this.formatDate(rate.effectiveFrom)}</span>
                </td>
            </tr>
        `;
    }

    bindTableEvents() {
        // Edit buttons
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.openEditModal(id);
            });
        });

        // Checkboxes for selection
        this.bindCheckboxEvents();
    }

    bindCheckboxEvents() {
        document.querySelectorAll('input[type="checkbox"][data-id]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const id = parseInt(checkbox.dataset.id);
                if (e.target.checked) {
                    this.selectedRates.add(id);
                } else {
                    this.selectedRates.delete(id);
                }
                this.updateItemSelection(id);
                this.updateToolbar();
            });
        });
    }

    // Grid View Rendering
    renderGrid() {
        const gridBody = document.getElementById('gridBody');
        const paginatedRates = this.getPaginatedRates();

        if (paginatedRates.length === 0) {
            gridBody.innerHTML = `
                <div class="grid-row">
                    <div class="grid-cell" style="grid-column: 1 / -1; text-align: center; padding: 2rem;">
                        <div class="empty-state">
                            <i class="fas fa-clock"></i>
                            <h3>No hourly rates found</h3>
                            <p>Click "Add" to create your first hourly rate.</p>
                        </div>
                    </div>
                </div>
            `;
            return;
        }

        gridBody.innerHTML = paginatedRates.map(rate => this.createGridRow(rate)).join('');
        this.bindGridEvents();
    }

    createGridRow(rate) {
        const isSelected = this.selectedRates.has(rate.id);
        return `
            <div class="grid-row ${isSelected ? 'selected' : ''}" data-id="${rate.id}">
                <div class="grid-cell">
                    <div class="grid-cell-actions">
                        <div class="checkbox-cell">
                            <input type="checkbox" ${isSelected ? 'checked' : ''} data-id="${rate.id}"
                                   aria-label="Select rate for deletion">
                        </div>
                        <button class="btn btn-action btn-edit edit-btn" title="Edit rate" data-id="${rate.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
                <div class="grid-cell">
                    <span class="rate-value">${rate.rate.toFixed(2)}</span>
                </div>
                <div class="grid-cell">
                    <span class="date-value">${this.formatDate(rate.effectiveFrom)}</span>
                </div>
            </div>
        `;
    }

    bindGridEvents() {
        // Edit buttons
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.openEditModal(id);
            });
        });

        this.bindCheckboxEvents();
    }

    // Card View Rendering
    renderCards() {
        const cardsContainer = document.getElementById('cardsContainer');
        const paginatedRates = this.getPaginatedRates();

        if (paginatedRates.length === 0) {
            cardsContainer.innerHTML = `
                <div class="empty-state" style="grid-column: 1 / -1;">
                    <i class="fas fa-clock"></i>
                    <h3>No hourly rates found</h3>
                    <p>Click "Add" to create your first hourly rate.</p>
                </div>
            `;
            return;
        }

        cardsContainer.innerHTML = paginatedRates.map(rate => this.createRateCard(rate)).join('');
        this.bindCardEvents();
    }

    createRateCard(rate) {
        const isSelected = this.selectedRates.has(rate.id);
        return `
            <div class="rate-card ${isSelected ? 'selected' : ''}" data-id="${rate.id}">
                <div class="rate-card-header">
                    <div class="rate-card-info">
                        <div class="rate-card-amount">$${rate.rate.toFixed(2)}</div>
                        <div class="rate-card-currency">USD per hour</div>
                    </div>
                    <div class="rate-card-actions">
                        <button class="btn btn-action btn-edit edit-btn" title="Edit rate" data-id="${rate.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
                <div class="rate-card-body">
                    <div class="rate-card-date">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Effective from</span>
                        <span class="rate-card-date-value">${this.formatDate(rate.effectiveFrom)}</span>
                    </div>
                </div>
                <div class="rate-card-footer">
                    <div class="rate-card-checkbox">
                        <input type="checkbox" ${isSelected ? 'checked' : ''} data-id="${rate.id}"
                               aria-label="Select rate for deletion">
                        <span>Select for bulk actions</span>
                    </div>
                </div>
            </div>
        `;
    }

    bindCardEvents() {
        // Edit buttons
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.openEditModal(id);
            });
        });

        this.bindCheckboxEvents();
    }

    updateItemSelection(id) {
        // Update selection for all view modes
        const selectors = [
            `tr[data-id="${id}"]`,           // Table row
            `.grid-row[data-id="${id}"]`,    // Grid row
            `.rate-card[data-id="${id}"]`    // Card
        ];

        selectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                if (this.selectedRates.has(id)) {
                    element.classList.add('selected');
                } else {
                    element.classList.remove('selected');
                }
            }
        });
    }

    updateToolbar() {
        const deleteBtn = document.getElementById('deleteBtn');
        deleteBtn.disabled = this.selectedRates.size === 0;
    }

    renderPagination() {
        const totalPages = this.getTotalPages();
        const startIndex = (this.currentPage - 1) * this.pageSize + 1;
        const endIndex = Math.min(this.currentPage * this.pageSize, this.rates.length);

        // Update pagination info - exact format from original
        document.getElementById('paginationInfo').textContent =
            `View ${startIndex} - ${endIndex} of ${this.rates.length}`;

        // Update current page and total pages
        document.getElementById('currentPage').textContent = this.currentPage;
        document.getElementById('totalPages').textContent = totalPages;

        // Update pagination buttons
        document.getElementById('firstPageBtn').disabled = this.currentPage === 1;
        document.getElementById('prevPageBtn').disabled = this.currentPage === 1;
        document.getElementById('nextPageBtn').disabled = this.currentPage === totalPages;
        document.getElementById('lastPageBtn').disabled = this.currentPage === totalPages;
    }

    goToPage(page) {
        const totalPages = this.getTotalPages();
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.render();
        }
    }

    formatDate(dateString) {
        // Format exactly like original: DD-MMM-YYYY (e.g., "29-Nov-2017")
        const date = new Date(dateString);
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        const day = date.getDate().toString().padStart(2, '0');
        const month = months[date.getMonth()];
        const year = date.getFullYear();

        return `${day}-${month}-${year}`;
    }

    // Modal Management
    openAddModal() {
        document.getElementById('modalTitle').textContent = 'Add Hourly Rate';
        document.getElementById('modalSaveBtn').innerHTML = '<i class="fas fa-save"></i> Save';
        this.clearForm();
        this.showModal();
    }

    openEditModal(id) {
        const rate = this.rates.find(r => r.id === id);
        if (!rate) return;

        document.getElementById('modalTitle').textContent = 'Edit Hourly Rate';
        document.getElementById('modalSaveBtn').innerHTML = '<i class="fas fa-save"></i> Update';

        document.getElementById('rateId').value = rate.id;
        document.getElementById('hourlyRate').value = rate.rate;
        document.getElementById('effectiveFrom').value = rate.effectiveFrom;

        this.showModal();
    }

    showModal() {
        const overlay = document.getElementById('modalOverlay');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Focus the first input
        setTimeout(() => {
            document.getElementById('hourlyRate').focus();
        }, 100);
    }

    closeModal() {
        const overlay = document.getElementById('modalOverlay');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
        this.clearForm();
    }

    clearForm() {
        document.getElementById('rateForm').reset();
        document.getElementById('rateId').value = '';
        this.clearFormErrors();
    }

    clearFormErrors() {
        document.querySelectorAll('.error-message').forEach(el => el.textContent = '');
        document.querySelectorAll('.error').forEach(el => el.classList.remove('error'));
    }

    // Form Validation and Saving
    validateForm() {
        const rate = document.getElementById('hourlyRate').value;
        const effectiveFrom = document.getElementById('effectiveFrom').value;
        let isValid = true;

        this.clearFormErrors();

        // Validate hourly rate
        if (!rate || parseFloat(rate) <= 0) {
            this.showFieldError('hourlyRate', 'Please enter a valid hourly rate greater than 0');
            isValid = false;
        } else if (parseFloat(rate) > 10000) {
            this.showFieldError('hourlyRate', 'Hourly rate cannot exceed $10,000');
            isValid = false;
        }

        // Validate effective date
        if (!effectiveFrom) {
            this.showFieldError('effectiveFrom', 'Please select an effective date');
            isValid = false;
        }

        return isValid;
    }

    showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorElement = document.getElementById(fieldId + 'Error');

        field.classList.add('error');
        errorElement.textContent = message;
    }

    saveRate() {
        if (!this.validateForm()) return;

        const rateId = document.getElementById('rateId').value;
        const rate = parseFloat(document.getElementById('hourlyRate').value);
        const effectiveFrom = document.getElementById('effectiveFrom').value;

        if (rateId) {
            // Update existing rate
            const index = this.rates.findIndex(r => r.id === parseInt(rateId));
            if (index !== -1) {
                this.rates[index] = { ...this.rates[index], rate, effectiveFrom };
                this.showToast('success', 'Rate Updated', 'Hourly rate has been updated successfully');
            }
        } else {
            // Add new rate
            const newRate = {
                id: this.getNextId(),
                rate,
                effectiveFrom
            };
            this.rates.push(newRate);
            this.showToast('success', 'Rate Added', 'New hourly rate has been added successfully');
        }

        this.saveToStorage();
        this.closeModal();
        this.render();
    }

    getNextId() {
        return this.rates.length > 0 ? Math.max(...this.rates.map(r => r.id)) + 1 : 1;
    }

    // Delete Operations
    deleteSelected() {
        if (this.selectedRates.size === 0) return;

        const count = this.selectedRates.size;
        this.showConfirmModal(
            'Delete Selected Rates',
            `Are you sure you want to delete ${count} selected rate${count > 1 ? 's' : ''}?`,
            () => {
                this.rates = this.rates.filter(r => !this.selectedRates.has(r.id));
                this.selectedRates.clear();
                this.saveToStorage();
                this.render();
                this.showToast('success', 'Rates Deleted', `${count} rate${count > 1 ? 's have' : ' has'} been deleted successfully`);
            }
        );
    }

    // Confirmation Modal
    showConfirmModal(title, message, onConfirm) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmMessage').textContent = message;

        const overlay = document.getElementById('confirmModalOverlay');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Remove any existing event listeners
        const confirmBtn = document.getElementById('confirmOkBtn');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        newConfirmBtn.addEventListener('click', () => {
            onConfirm();
            this.closeConfirmModal();
        });
    }

    closeConfirmModal() {
        const overlay = document.getElementById('confirmModalOverlay');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    // Toast Notifications
    showToast(type, title, message) {
        const toastContainer = document.getElementById('toastContainer');
        const toastId = 'toast-' + Date.now();

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.id = toastId;
        toast.innerHTML = `
            <i class="toast-icon ${iconMap[type] || iconMap.info}"></i>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        toastContainer.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
    }

    // Data Operations
    saveData() {
        this.saveToStorage();
        this.showToast('success', 'Data Saved', 'All changes have been saved successfully');
    }

    refresh() {
        this.selectedRates.clear();
        this.currentPage = 1;
        this.loadFromStorage();
        this.render();
        this.showToast('info', 'Data Refreshed', 'Data has been refreshed from storage');
    }

    // Data Persistence
    saveToStorage() {
        try {
            localStorage.setItem('hourlyRatesOriginal', JSON.stringify(this.rates));
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
            this.showToast('error', 'Save Failed', 'Failed to save data to local storage');
        }
    }

    loadFromStorage() {
        try {
            const stored = localStorage.getItem('hourlyRatesOriginal');
            if (stored) {
                this.rates = JSON.parse(stored);
            }

            // Load view mode preference
            const storedViewMode = localStorage.getItem('hourlyRatesViewMode');
            if (storedViewMode && ['list', 'grid', 'card'].includes(storedViewMode)) {
                this.currentView = storedViewMode;
                this.updateViewModeButtons();
                this.switchViewContainer();
            }
        } catch (error) {
            console.error('Failed to load from localStorage:', error);
            this.showToast('error', 'Load Failed', 'Failed to load data from local storage');
        }
    }

    saveViewModeToStorage() {
        try {
            localStorage.setItem('hourlyRatesViewMode', this.currentView);
        } catch (error) {
            console.error('Failed to save view mode to localStorage:', error);
        }
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.hourlyRateApp = new HourlyRateApp();
});
