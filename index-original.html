<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hourly Rate</title>
    <link rel="stylesheet" href="styles-original.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header with Title -->
        <header class="app-header">
            <h1 class="app-title">Hourly Rate</h1>
        </header>

        <!-- Toolbar -->
        <div class="toolbar">
            <div class="toolbar-section">
                <button class="btn btn-primary" id="addBtn" title="Add new rate">
                    <i class="fas fa-plus"></i>
                    Add
                </button>
                <button class="btn btn-danger" id="deleteBtn" title="Delete selected rates" disabled>
                    <i class="fas fa-trash"></i>
                    Delete
                </button>
                <button class="btn btn-success" id="saveBtn" title="Save changes">
                    <i class="fas fa-save"></i>
                    Save
                </button>
                <button class="btn btn-secondary" id="refreshBtn" title="Refresh data">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>

            <div class="toolbar-section">
                <div class="view-mode-selector">
                    <span class="view-mode-label">View:</span>
                    <div class="view-mode-buttons">
                        <button class="btn-view-mode active" id="listViewBtn" data-view="list" title="List View">
                            <i class="fas fa-list"></i>
                        </button>
                        <button class="btn-view-mode" id="gridViewBtn" data-view="grid" title="Grid View">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="btn-view-mode" id="cardViewBtn" data-view="card" title="Card View">
                            <i class="fas fa-th-large"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Container -->
        <div class="data-container">
            <!-- List View (Table) -->
            <div class="view-container list-view active" id="listView">
                <div class="table-container">
                    <table class="data-table" id="dataTable">
                        <thead>
                            <tr>
                                <th class="col-edit">Edit</th>
                                <th class="col-delete">Delete</th>
                                <th class="col-rate">
                                    Hourly Rate
                                    <span class="required-indicator">*</span>
                                </th>
                                <th class="col-date">
                                    Effective From
                                    <span class="required-indicator">*</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- Data rows will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Grid View -->
            <div class="view-container grid-view" id="gridView">
                <div class="grid-container">
                    <div class="grid-header">
                        <div class="grid-header-cell">Actions</div>
                        <div class="grid-header-cell">Hourly Rate *</div>
                        <div class="grid-header-cell">Effective From *</div>
                    </div>
                    <div class="grid-body" id="gridBody">
                        <!-- Grid items will be inserted here -->
                    </div>
                </div>
            </div>

            <!-- Card View -->
            <div class="view-container card-view" id="cardView">
                <div class="cards-container" id="cardsContainer">
                    <!-- Cards will be inserted here -->
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">View 1 - 10 of 74</span>
            </div>
            <div class="pagination-controls">
                <button class="btn-page" id="firstPageBtn" title="First page" disabled>
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button class="btn-page" id="prevPageBtn" title="Previous page" disabled>
                    <i class="fas fa-angle-left"></i>
                </button>
                <span class="page-info">
                    Page <span id="currentPage">1</span> of <span id="totalPages">8</span>
                </span>
                <button class="btn-page" id="nextPageBtn" title="Next page">
                    <i class="fas fa-angle-right"></i>
                </button>
                <button class="btn-page" id="lastPageBtn" title="Last page">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
            <div class="page-size-selector">
                <select id="pageSize" aria-label="Items per page">
                    <option value="10" selected>10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal" role="dialog" aria-labelledby="modalTitle" aria-modal="true">
            <div class="modal-header">
                <h2 id="modalTitle">Add Hourly Rate</h2>
                <button class="btn-close" id="modalCloseBtn" aria-label="Close modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="modal-body" id="rateForm" novalidate>
                <div class="form-group">
                    <label for="hourlyRate" class="required">Hourly Rate</label>
                    <div class="input-group">
                        <input type="number" id="hourlyRate" name="hourlyRate" step="0.01" min="0" required
                               aria-describedby="hourlyRateError" placeholder="0.00">
                    </div>
                    <div class="error-message" id="hourlyRateError"></div>
                </div>
                <div class="form-group">
                    <label for="effectiveFrom" class="required">Effective From</label>
                    <input type="date" id="effectiveFrom" name="effectiveFrom" required
                           aria-describedby="effectiveFromError">
                    <div class="error-message" id="effectiveFromError"></div>
                </div>
                <input type="hidden" id="rateId" name="rateId">
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="modalCancelBtn">Cancel</button>
                <button type="submit" class="btn btn-primary" id="modalSaveBtn" form="rateForm">
                    <i class="fas fa-save"></i>
                    Save
                </button>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal-overlay" id="confirmModalOverlay">
        <div class="modal confirm-modal" role="dialog" aria-labelledby="confirmTitle" aria-modal="true">
            <div class="modal-header">
                <h2 id="confirmTitle">Confirm Action</h2>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">Are you sure you want to proceed?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="confirmCancelBtn">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmOkBtn">
                    <i class="fas fa-check"></i>
                    Confirm
                </button>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="script-original.js"></script>
</body>
</html>
