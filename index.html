<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hourly Rate Manager</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-clock"></i>
                    Hourly Rate
                </h1>
                <div class="header-actions">
                    <button class="btn btn-primary" id="addBtn">
                        <i class="fas fa-plus"></i>
                        Add Rate
                    </button>
                    <button class="btn btn-secondary" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <div class="settings-container">
                        <button class="btn btn-secondary" id="settingsBtn" title="Customization settings" aria-label="Open settings panel">
                            <i class="fas fa-cog"></i>
                            Settings
                            <i class="fas fa-chevron-up settings-chevron"></i>
                        </button>
                        <!-- Settings Dropdown Panel -->
                        <div class="settings-panel" id="settingsPanel" role="dialog" aria-labelledby="settingsBtn" aria-hidden="true">
                            <div class="settings-content">
                                <!-- Themes Section -->
                                <div class="settings-section">
                                    <div class="settings-section-header">
                                        <i class="fas fa-palette"></i>
                                        <span class="settings-section-title">THEMES</span>
                                    </div>
                                    <div class="settings-options">
                                        <div class="theme-option active" data-theme="white">
                                            <div class="theme-color white"></div>
<header class="header" role="banner">
    <div class="header-content">
        <h1 class="app-title" role="heading" aria-level="1">
            <i class="fas fa-clock" aria-hidden="true"></i>
            Hourly Rate
        </h1>
        <div class="header-actions">
            <button class="btn btn-primary" id="addBtn" role="button" aria-label="Add new rate">
                <i class="fas fa-plus" aria-hidden="true"></i>
                Add Rate
            </button>
            <button class="btn btn-secondary" id="refreshBtn" role="button" aria-label="Refresh rates">
                <i class="fas fa-sync-alt" aria-hidden="true"></i>
                Refresh
            </button>
            <div class="settings-container">
                <button class="btn btn-secondary" id="settingsBtn" role="button" aria-label="Open settings panel" aria-expanded="false" aria-controls="settingsPanel">
                    <i class="fas fa-cog" aria-hidden="true"></i>
                    Settings
                    <i class="fas fa-chevron-up settings-chevron" aria-hidden="true"></i>
                </button>
                <!-- Settings Dropdown Panel -->
                <div class="settings-panel" id="settingsPanel" role="dialog" aria-labelledby="settingsBtn" aria-hidden="true">
                    <!-- Settings content remains the same -->
                </div>
            </div>
        </div>
    </div>
</header>
```

This improved version includes:

* Added `role` attributes to define the purpose of each element (e.g., `role="banner"` for the header, `role="heading"` for the h1, `role="button"` for the buttons).
* Added `aria-label` attributes to provide a text description of each button's purpose.
* Added `aria-hidden="true"` to the icons to indicate that they are decorative and should not be announced by screen readers.
* Added `aria-expanded` and `aria-controls` attributes to the settings button to define its relationship with the settings panel.
* Added `role="dialog"` to the settings panel to define its purpose as a dialog box.Since there is no selected code, I will provide a general improvement to the HTML structure and accessibility. Here's an example of how the header section can be improved:

```html
<!-- Header -->
<header class="header" role="banner">
    <div class="header-content">
        <h1 class="app-title" aria-level="1">
            <i class="fas fa-clock" aria-hidden="true"></i>
            Hourly Rate
        </h1>
        <div class="header-actions">
            <button class="btn btn-primary" id="addBtn" aria-label="Add new hourly rate">
                <i class="fas fa-plus" aria-hidden="true"></i>
                Add Rate
            </button>
            <button class="btn btn-secondary" id="refreshBtn" aria-label="Refresh hourly rates">
                <i class="fas fa-sync-alt" aria-hidden="true"></i>
                Refresh
            </button>
            <div class="settings-container">
                <button class="btn btn-secondary" id="settingsBtn" title="Customization settings" aria-label="Open settings panel" aria-expanded="false">
                    <i class="fas fa-cog" aria-hidden="true"></i>
                    Settings
                    <i class="fas fa-chevron-up settings-chevron" aria-hidden="true"></i>
                </button>
                <!-- Settings Dropdown Panel -->
                <div class="settings-panel" id="settingsPanel" role="dialog" aria-labelledby="settingsBtn" aria-hidden="true">
                    <!-- Settings content remains the same -->
                </div>
            </div>
        </div>
    </div>
</header>
```

This improved version includes:

* Added `role="banner"` to the header element for better accessibility.
* Added `aria-level="1"` to the h1 element to define its level in the document outline.
* Added `aria-hidden="true"` to the icons to indicate that they are decorative and should be ignored by screen readers.
* Added `aria-label` attributes to the buttons to provide a clear and concise description of their purpose.
* Added `aria-expanded="false"` to the settings button to indicate its initial state.
* Added `title` attributes to the buttons to provide a tooltip or description when hovered over.                                           <span>White</span>
                                            <i class="fas fa-check theme-check"></i>
                                        </div>
                                        <div class="theme-option" data-theme="dark">
                                            <div class="theme-color dark"></div>
                                            <span>Dark</span>
                                            <i class="fas fa-check theme-check"></i>
                                        </div>
                                        <div class="theme-option" data-theme="blue">
                                            <div class="theme-color blue"></div>
                                            <span>Blue</span>
                                            <i class="fas fa-check theme-check"></i>
                                        </div>
                                        <div class="theme-option" data-theme="green">
                                            <div class="theme-color green"></div>
                                            <span>Green</span>
                                            <i class="fas fa-check theme-check"></i>
                                        </div>
                                        <div class="theme-option" data-theme="purple">
                                            <div class="theme-color purple"></div>
                                            <span>Purple</span>
                                            <i class="fas fa-check theme-check"></i>
                                        </div>
                                    </div>
                                </div>

                                <!-- View Mode Section -->
                                <div class="settings-section">
                                    <div class="settings-section-header">
                                        <i class="fas fa-eye"></i>
                                        <span class="settings-section-title">VIEW MODE</span>
                                    </div>
                                    <div class="settings-options">
                                        <div class="view-mode-option" data-view="list">
                                            <i class="fas fa-list"></i>
                                            <span>List View</span>
                                            <i class="fas fa-check view-check"></i>
                                        </div>
                                        <div class="view-mode-option active" data-view="grid">
                                            <i class="fas fa-th"></i>
                                            <span>Grid View</span>
                                            <i class="fas fa-check view-check"></i>
                                        </div>
                                        <div class="view-mode-option" data-view="card">
                                            <i class="fas fa-th-large"></i>
                                            <span>Card View</span>
                                            <i class="fas fa-check view-check"></i>
                                        </div>
                                    </div>
                                </div>

                                <!-- Fonts Section -->
                                <div class="settings-section">
                                    <div class="settings-section-header">
                                        <i class="fas fa-font"></i>
                                        <span class="settings-section-title">FONTS</span>
                                    </div>
                                    <div class="settings-options">
                                        <div class="font-option active" data-font="times">
                                            <span style="font-family: 'Times New Roman', serif;">Times New Roman</span>
                                            <i class="fas fa-check font-check"></i>
                                        </div>
                                        <div class="font-option" data-font="arial">
                                            <span style="font-family: Arial, sans-serif;">Arial</span>
                                            <i class="fas fa-check font-check"></i>
                                        </div>
                                        <div class="font-option" data-font="helvetica">
                                            <span style="font-family: 'Helvetica Neue', sans-serif;">Helvetica</span>
                                            <i class="fas fa-check font-check"></i>
                                        </div>
                                        <div class="font-option" data-font="georgia">
                                            <span style="font-family: Georgia, serif;">Georgia</span>
                                            <i class="fas fa-check font-check"></i>
                                        </div>
                                        <div class="font-option" data-font="verdana">
                                            <span style="font-family: Verdana, sans-serif;">Verdana</span>
                                            <i class="fas fa-check font-check"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="currency-container">
                        <button class="btn btn-secondary" id="currencyBtn" title="Select currency" aria-label="Select currency">
                            <i class="fas fa-dollar-sign"></i>
                            <span id="currentCurrency">USD</span>
                            <i class="fas fa-chevron-down currency-chevron"></i>
                        </button>
                        <!-- Currency Dropdown Panel -->
                        <div class="currency-panel" id="currencyPanel" role="dialog" aria-labelledby="currencyBtn" aria-hidden="true">
                            <div class="currency-content">
                                <div class="currency-header">
                                    <i class="fas fa-coins"></i>
                                    <span class="currency-title">SELECT CURRENCY</span>
                                </div>
                                <div class="currency-options">
                                    <div class="currency-option active" data-currency="USD" data-symbol="$" data-rate="1.0">
                                        <div class="currency-info">
                                            <span class="currency-symbol">$</span>
                                            <div class="currency-details">
                                                <span class="currency-code">USD</span>
                                                <span class="currency-name">US Dollar</span>
                                            </div>
                                        </div>
                                        <i class="fas fa-check currency-check"></i>
                                    </div>
                                    <div class="currency-option" data-currency="INR" data-symbol="₹" data-rate="83.12">
                                        <div class="currency-info">
                                            <span class="currency-symbol">₹</span>
                                            <div class="currency-details">
                                                <span class="currency-code">INR</span>
                                                <span class="currency-name">Indian Rupee</span>
                                            </div>
                                        </div>
                                        <i class="fas fa-check currency-check"></i>
                                    </div>
                                    <div class="currency-option" data-currency="EUR" data-symbol="€" data-rate="0.92">
                                        <div class="currency-info">
                                            <span class="currency-symbol">€</span>
                                            <div class="currency-details">
                                                <span class="currency-code">EUR</span>
                                                <span class="currency-name">Euro</span>
                                            </div>
                                        </div>
                                        <i class="fas fa-check currency-check"></i>
                                    </div>
                                    <div class="currency-option" data-currency="GBP" data-symbol="£" data-rate="0.79">
                                        <div class="currency-info">
                                            <span class="currency-symbol">£</span>
                                            <div class="currency-details">
                                                <span class="currency-code">GBP</span>
                                                <span class="currency-name">British Pound</span>
                                            </div>
                                        </div>
                                        <i class="fas fa-check currency-check"></i>
                                    </div>
                                    <div class="currency-option" data-currency="CAD" data-symbol="C$" data-rate="1.36">
                                        <div class="currency-info">
                                            <span class="currency-symbol">C$</span>
                                            <div class="currency-details">
                                                <span class="currency-code">CAD</span>
                                                <span class="currency-name">Canadian Dollar</span>
                                            </div>
                                        </div>
                                        <i class="fas fa-check currency-check"></i>
                                    </div>
                                    <div class="currency-option" data-currency="AUD" data-symbol="A$" data-rate="1.52">
                                        <div class="currency-info">
                                            <span class="currency-symbol">A$</span>
                                            <div class="currency-details">
                                                <span class="currency-code">AUD</span>
                                                <span class="currency-name">Australian Dollar</span>
                                            </div>
                                        </div>
                                        <i class="fas fa-check currency-check"></i>
                                    </div>
                                </div>
                                <div class="currency-footer">
                                    <small class="exchange-rate-note">Exchange rates are approximate and for demo purposes</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Search and Filter Section -->
        <section class="controls">
            <div class="search-container">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="Search rates..." aria-label="Search rates">
                </div>
                <div class="filter-controls">
                    <select id="sortBy" aria-label="Sort by">
                        <option value="date-desc">Latest First</option>
                        <option value="date-asc">Oldest First</option>
                        <option value="rate-desc">Highest Rate</option>
                        <option value="rate-asc">Lowest Rate</option>
                    </select>
                </div>
            </div>
            <div class="controls-right">
                <!-- View Mode Selector -->
                <div class="view-mode-selector">
                    <span class="view-mode-label">View:</span>
                    <div class="view-mode-buttons">
                        <button class="btn-view-mode active" id="listViewBtn" data-view="list" title="List View" aria-label="Switch to list view">
                            <i class="fas fa-list"></i>
                        </button>
                        <button class="btn-view-mode" id="gridViewBtn" data-view="grid" title="Grid View" aria-label="Switch to grid view">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="btn-view-mode" id="cardViewBtn" data-view="card" title="Card View" aria-label="Switch to card view">
                            <i class="fas fa-th-large"></i>
                        </button>
                    </div>
                </div>
                <!-- Bulk Actions -->
                <div class="bulk-actions">
                    <button class="btn btn-danger" id="deleteSelectedBtn" disabled>
                        <i class="fas fa-trash"></i>
                        Delete Selected
                    </button>
                    <span class="selected-count" id="selectedCount">0 selected</span>
                </div>
            </div>
        </section>

        <!-- Main Content -->
        <main class="main-content">
            <!-- List View -->
            <div class="view-container list-view active" id="listView">
                <div class="table-container">
                    <table class="data-table" id="dataTable">
                        <thead>
                            <tr>
                                <th class="col-select">
                                    <input type="checkbox" id="selectAllCheckbox" aria-label="Select all rates">
                                </th>
                                <th class="col-rate sortable" data-sort="rate" role="button" tabindex="0"
                                    aria-label="Sort by hourly rate" title="Click to sort by hourly rate">
                                    <span class="sort-header">
                                        <span>Hourly Rate</span>
                                        <span class="sort-icon">
                                            <i class="fas fa-sort sort-neutral"></i>
                                            <i class="fas fa-sort-up sort-asc"></i>
                                            <i class="fas fa-sort-down sort-desc"></i>
                                        </span>
                                    </span>
                                </th>
                                <th class="col-date sortable" data-sort="date" role="button" tabindex="0"
                                    aria-label="Sort by effective date" title="Click to sort by effective date">
                                    <span class="sort-header">
                                        <span>Effective From</span>
                                        <span class="sort-icon">
                                            <i class="fas fa-sort sort-neutral"></i>
                                            <i class="fas fa-sort-up sort-asc"></i>
                                            <i class="fas fa-sort-down sort-desc"></i>
                                        </span>
                                    </span>
                                </th>
                                <th class="col-actions">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- Table rows will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Grid View -->
            <div class="view-container grid-view" id="gridView">
                <div class="grid-view-header">
                    <div class="grid-sort-controls">
                        <span class="sort-label">Sort by:</span>
                        <button class="btn-sort sortable" data-sort="rate" role="button" tabindex="0"
                                aria-label="Sort by hourly rate" title="Click to sort by hourly rate">
                            <span>Rate</span>
                            <span class="sort-icon">
                                <i class="fas fa-sort sort-neutral"></i>
                                <i class="fas fa-sort-up sort-asc"></i>
                                <i class="fas fa-sort-down sort-desc"></i>
                            </span>
                        </button>
                        <button class="btn-sort sortable" data-sort="date" role="button" tabindex="0"
                                aria-label="Sort by effective date" title="Click to sort by effective date">
                            <span>Date</span>
                            <span class="sort-icon">
                                <i class="fas fa-sort sort-neutral"></i>
                                <i class="fas fa-sort-up sort-asc"></i>
                                <i class="fas fa-sort-down sort-desc"></i>
                            </span>
                        </button>
                    </div>
                </div>
                <div class="grid-container" id="gridContainer">
                    <!-- Grid items will be inserted here -->
                </div>
            </div>

            <!-- Card View -->
            <div class="view-container card-view" id="cardView">
                <div class="card-view-header">
                    <div class="card-sort-controls">
                        <span class="sort-label">Sort by:</span>
                        <button class="btn-sort sortable" data-sort="rate" role="button" tabindex="0"
                                aria-label="Sort by hourly rate" title="Click to sort by hourly rate">
                            <span>Rate</span>
                            <span class="sort-icon">
                                <i class="fas fa-sort sort-neutral"></i>
                                <i class="fas fa-sort-up sort-asc"></i>
                                <i class="fas fa-sort-down sort-desc"></i>
                            </span>
                        </button>
                        <button class="btn-sort sortable" data-sort="date" role="button" tabindex="0"
                                aria-label="Sort by effective date" title="Click to sort by effective date">
                            <span>Date</span>
                            <span class="sort-icon">
                                <i class="fas fa-sort sort-neutral"></i>
                                <i class="fas fa-sort-up sort-asc"></i>
                                <i class="fas fa-sort-down sort-desc"></i>
                            </span>
                        </button>
                    </div>
                </div>
                <div class="cards-container" id="cardsContainer">
                    <!-- Cards will be inserted here -->
                </div>
            </div>

            <!-- Empty State -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-clock"></i>
                <h3>No hourly rates found</h3>
                <p>Get started by adding your first hourly rate.</p>
                <button class="btn btn-primary" onclick="openAddModal()">
                    <i class="fas fa-plus"></i>
                    Add Your First Rate
                </button>
            </div>
        </main>

        <!-- Pagination -->
        <footer class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Showing 1-10 of 0 rates</span>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-icon" id="firstPageBtn" title="First page">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button class="btn btn-icon" id="prevPageBtn" title="Previous page">
                    <i class="fas fa-angle-left"></i>
                </button>
                <div class="page-numbers" id="pageNumbers"></div>
                <button class="btn btn-icon" id="nextPageBtn" title="Next page">
                    <i class="fas fa-angle-right"></i>
                </button>
                <button class="btn btn-icon" id="lastPageBtn" title="Last page">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
            <div class="page-size-selector">
                <label for="pageSize">Show:</label>
                <select id="pageSize">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
        </footer>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal" role="dialog" aria-labelledby="modalTitle" aria-modal="true">
            <div class="modal-header">
                <h2 id="modalTitle">Add Hourly Rate</h2>
                <button class="btn btn-icon modal-close" id="modalCloseBtn" aria-label="Close modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="modal-body" id="rateForm" novalidate>
                <div class="form-group">
                    <label for="hourlyRate" class="required">Hourly Rate</label>
                    <div class="input-group">
                        <span class="input-prefix" id="currencyPrefix">$</span>
                        <input type="number" id="hourlyRate" name="hourlyRate" step="0.01" min="0" required
                               aria-describedby="hourlyRateError" placeholder="0.00">
                    </div>
                    <small class="form-help" id="currencyHelp">Enter rate in USD (base currency). Display will convert to selected currency.</small>
                    <div class="error-message" id="hourlyRateError"></div>
                </div>
                <div class="form-group">
                    <label for="effectiveFrom" class="required">Effective From</label>
                    <input type="date" id="effectiveFrom" name="effectiveFrom" required
                           aria-describedby="effectiveFromError">
                    <div class="error-message" id="effectiveFromError"></div>
                </div>
                <input type="hidden" id="rateId" name="rateId">
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="modalCancelBtn">Cancel</button>
                <button type="submit" class="btn btn-primary" id="modalSaveBtn" form="rateForm">
                    <i class="fas fa-save"></i>
                    Save Rate
                </button>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal-overlay" id="confirmModalOverlay">
        <div class="modal confirm-modal" role="dialog" aria-labelledby="confirmTitle" aria-modal="true">
            <div class="modal-header">
                <h2 id="confirmTitle">Confirm Action</h2>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">Are you sure you want to proceed?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="confirmCancelBtn">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmOkBtn">
                    <i class="fas fa-check"></i>
                    Confirm
                </button>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="script.js"></script>
</body>
</html>
