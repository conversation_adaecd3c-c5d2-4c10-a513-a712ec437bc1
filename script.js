// Hourly Rate Manager Application
class HourlyRateManager {
    constructor() {
        this.rates = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortBy = 'date-desc';
        this.searchTerm = '';
        this.selectedRates = new Set();
        this.currentView = 'list'; // Default view mode
        this.currentTheme = 'white'; // Default theme
        this.currentFont = 'times'; // Default font
        this.currentSort = { column: null, direction: null }; // Sorting state
        this.currentCurrency = 'USD'; // Current selected currency
        this.baseCurrency = 'USD'; // Base currency for all stored rates
        this.exchangeRates = { // Exchange rates relative to USD
            'USD': 1.0,
            'INR': 83.12,
            'EUR': 0.92,
            'GBP': 0.79,
            'CAD': 1.36,
            'AUD': 1.52
        };
        this.currencySymbols = {
            'USD': '$',
            'INR': '₹',
            'EUR': '€',
            'GBP': '£',
            'CAD': 'C$',
            'AUD': 'A$'
        };
        this.conversionCache = new Map(); // Cache for currency conversions

        this.init();
        this.loadSampleData();
    }

    init() {
        this.cacheElements();
        this.bindEvents();
        this.loadFromStorage();
        this.render();
    }

    bindEvents() {
        // Header actions
        document.getElementById('addBtn').addEventListener('click', () => this.openAddModal());
        document.getElementById('refreshBtn').addEventListener('click', () => this.refresh());
        document.getElementById('settingsBtn').addEventListener('click', () => this.toggleSettingsPanel());
        document.getElementById('currencyBtn').addEventListener('click', () => this.toggleCurrencyPanel());

        // Search and filter with debouncing
        document.getElementById('searchInput').addEventListener('input', this.debounce((e) => {
            this.searchTerm = e.target.value.toLowerCase();
            this.currentPage = 1;
            this.render();
        }, 300));

        document.getElementById('sortBy').addEventListener('change', (e) => {
            this.sortBy = e.target.value;
            this.render();
        });

        // View mode buttons
        document.getElementById('listViewBtn').addEventListener('click', () => this.setViewMode('list'));
        document.getElementById('gridViewBtn').addEventListener('click', () => this.setViewMode('grid'));
        document.getElementById('cardViewBtn').addEventListener('click', () => this.setViewMode('card'));

        // Bulk actions
        document.getElementById('deleteSelectedBtn').addEventListener('click', () => this.deleteSelected());

        // Select All functionality
        document.getElementById('selectAllCheckbox').addEventListener('change', (e) => this.handleSelectAll(e.target.checked));
        document.getElementById('selectAllGridCheckbox').addEventListener('change', (e) => this.handleSelectAll(e.target.checked));
        document.getElementById('selectAllCardCheckbox').addEventListener('change', (e) => this.handleSelectAll(e.target.checked));

        // Pagination
        document.getElementById('firstPageBtn').addEventListener('click', () => this.goToPage(1));
        document.getElementById('prevPageBtn').addEventListener('click', () => this.goToPage(this.currentPage - 1));
        document.getElementById('nextPageBtn').addEventListener('click', () => this.goToPage(this.currentPage + 1));
        document.getElementById('lastPageBtn').addEventListener('click', () => this.goToPage(this.getTotalPages()));
        document.getElementById('pageSize').addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 1;
            this.render();
        });

        // Modal events
        document.getElementById('modalCloseBtn').addEventListener('click', () => this.closeModal());
        document.getElementById('modalCancelBtn').addEventListener('click', () => this.closeModal());
        document.getElementById('modalOverlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) this.closeModal();
        });

        // Form submission
        document.getElementById('rateForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveRate();
        });

        // Confirmation modal
        document.getElementById('confirmCancelBtn').addEventListener('click', () => this.closeConfirmModal());
        document.getElementById('confirmModalOverlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) this.closeConfirmModal();
        });

        // Lazy load panel events (bind only when needed)
        this.settingsEventsbound = false;
        this.currencyEventsBound = false;

        // Sortable headers
        this.bindSortableHeaders();

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
                this.closeConfirmModal();
                this.closeSettingsPanel();
                this.closeCurrencyPanel();
            }
        });

        // Close panels when clicking outside
        document.addEventListener('click', (e) => {
            const settingsContainer = document.querySelector('.settings-container');
            const currencyContainer = document.querySelector('.currency-container');

            if (settingsContainer && !settingsContainer.contains(e.target)) {
                this.closeSettingsPanel();
            }

            if (currencyContainer && !currencyContainer.contains(e.target)) {
                this.closeCurrencyPanel();
            }
        });
    }

    loadSampleData() {
        // Load sample data with all dates in 2025
        if (this.rates.length === 0) {
            this.rates = [
                { id: 1, rate: 125.00, effectiveFrom: '2025-01-01' },
                { id: 2, rate: 125.00, effectiveFrom: '2025-01-15' },
                { id: 3, rate: 125.00, effectiveFrom: '2025-02-01' },
                { id: 4, rate: 127.00, effectiveFrom: '2025-02-15' },
                { id: 5, rate: 128.00, effectiveFrom: '2025-03-01' },
                { id: 6, rate: 130.00, effectiveFrom: '2025-03-15' },
                { id: 7, rate: 132.00, effectiveFrom: '2025-04-01' },
                { id: 8, rate: 133.00, effectiveFrom: '2025-04-15' },
                { id: 9, rate: 133.00, effectiveFrom: '2025-05-01' },
                { id: 10, rate: 133.00, effectiveFrom: '2025-05-15' },
                { id: 11, rate: 135.00, effectiveFrom: '2025-06-01' },
                { id: 12, rate: 140.00, effectiveFrom: '2025-06-15' },
                { id: 13, rate: 145.00, effectiveFrom: '2025-07-01' },
                { id: 14, rate: 150.00, effectiveFrom: '2025-07-15' },
                { id: 15, rate: 155.00, effectiveFrom: '2025-08-01' },
                { id: 16, rate: 160.00, effectiveFrom: '2025-08-15' },
                { id: 17, rate: 165.00, effectiveFrom: '2025-09-01' },
                { id: 18, rate: 170.00, effectiveFrom: '2025-09-15' }
            ];
            this.saveToStorage();
        }
    }

    getFilteredRates() {
        let filtered = this.rates.filter(rate => {
            const searchMatch = this.searchTerm === '' ||
                rate.rate.toString().includes(this.searchTerm) ||
                this.formatDate(rate.effectiveFrom).toLowerCase().includes(this.searchTerm);
            return searchMatch;
        });

        // Apply column sorting if active
        if (this.currentSort.column && this.currentSort.direction) {
            filtered.sort((a, b) => {
                let comparison = 0;

                if (this.currentSort.column === 'rate') {
                    comparison = a.rate - b.rate;
                } else if (this.currentSort.column === 'date') {
                    comparison = new Date(a.effectiveFrom) - new Date(b.effectiveFrom);
                }

                return this.currentSort.direction === 'asc' ? comparison : -comparison;
            });
        } else {
            // Fallback to legacy sortBy for backward compatibility
            filtered.sort((a, b) => {
                switch (this.sortBy) {
                    case 'date-asc':
                        return new Date(a.effectiveFrom) - new Date(b.effectiveFrom);
                    case 'date-desc':
                        return new Date(b.effectiveFrom) - new Date(a.effectiveFrom);
                    case 'rate-asc':
                        return a.rate - b.rate;
                    case 'rate-desc':
                        return b.rate - a.rate;
                    default:
                        return new Date(b.effectiveFrom) - new Date(a.effectiveFrom);
                }
            });
        }

        return filtered;
    }

    getPaginatedRates() {
        const filtered = this.getFilteredRates();
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        return filtered.slice(startIndex, endIndex);
    }

    getTotalPages() {
        const filtered = this.getFilteredRates();
        return Math.ceil(filtered.length / this.pageSize);
    }

    render() {
        this.renderCurrentView();
        this.renderPagination();
        this.updateBulkActions();
    }

    // View Mode Management
    setViewMode(viewMode) {
        this.currentView = viewMode;
        this.updateViewModeButtons();
        this.switchViewContainer();
        this.renderCurrentView();
        this.updateBulkActions(); // Update select all checkboxes for new view
        this.saveViewModeToStorage();
    }

    updateViewModeButtons() {
        // Remove active class from all buttons
        document.querySelectorAll('.btn-view-mode').forEach(btn => {
            btn.classList.remove('active');
        });

        // Add active class to current view button
        const activeBtn = document.querySelector(`[data-view="${this.currentView}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
    }

    switchViewContainer() {
        // Hide all view containers
        document.querySelectorAll('.view-container').forEach(container => {
            container.classList.remove('active');
        });

        // Show current view container
        const currentContainer = document.getElementById(`${this.currentView}View`);
        if (currentContainer) {
            currentContainer.classList.add('active');
        }
    }

    renderCurrentView() {
        const paginatedRates = this.getPaginatedRates();
        const emptyState = document.getElementById('emptyState');

        if (paginatedRates.length === 0) {
            // Hide all view containers and show empty state
            document.querySelectorAll('.view-container').forEach(container => {
                container.style.display = 'none';
            });
            emptyState.style.display = 'block';
            return;
        }

        // Show current view container and hide empty state
        document.querySelectorAll('.view-container').forEach(container => {
            container.style.display = container.classList.contains('active') ? 'block' : 'none';
        });
        emptyState.style.display = 'none';

        switch (this.currentView) {
            case 'list':
                this.renderListView(paginatedRates);
                break;
            case 'grid':
                this.renderGridView(paginatedRates);
                break;
            case 'card':
                this.renderCardView(paginatedRates);
                break;
            default:
                this.renderListView(paginatedRates);
        }
    }

    renderListView(rates) {
        // Use cached element and batch DOM updates
        const fragment = document.createDocumentFragment();
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = rates.map(rate => this.createTableRow(rate)).join('');

        while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
        }

        this.elements.tableBody.innerHTML = '';
        this.elements.tableBody.appendChild(fragment);
        this.bindTableEvents();

        // Rebind sortable headers and update indicators
        this.bindSortableHeaders();
        this.updateSortIndicators();
    }

    renderGridView(rates) {
        // Use cached element and batch DOM updates
        const fragment = document.createDocumentFragment();
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = rates.map(rate => this.createGridItem(rate)).join('');

        while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
        }

        this.elements.gridContainer.innerHTML = '';
        this.elements.gridContainer.appendChild(fragment);
        this.bindGridEvents();

        // Rebind sortable headers and update indicators
        this.bindSortableHeaders();
        this.updateSortIndicators();
    }

    renderCardView(rates) {
        // Use cached element and batch DOM updates
        const fragment = document.createDocumentFragment();
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = rates.map(rate => this.createRateCard(rate)).join('');

        while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
        }

        this.elements.cardsContainer.innerHTML = '';
        this.elements.cardsContainer.appendChild(fragment);
        this.bindCardEvents();

        // Rebind sortable headers and update indicators
        this.bindSortableHeaders();
        this.updateSortIndicators();
    }

    createTableRow(rate) {
        const isSelected = this.selectedRates.has(rate.id);
        const formattedRate = this.formatCurrency(rate.rate);
        const formattedDate = this.formatDate(rate.effectiveFrom);

        return `
            <tr class="${isSelected ? 'selected' : ''}" data-id="${rate.id}">
                <td class="col-select">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} data-id="${rate.id}" aria-label="Select rate ${formattedRate}">
                </td>
                <td class="col-rate">
                    <span class="rate-amount" title="Exchange rate: 1 USD = ${this.exchangeRates[this.currentCurrency]} ${this.currentCurrency}">${formattedRate}</span>
                </td>
                <td class="col-date">
                    <span class="rate-date">${formattedDate}</span>
                </td>
                <td class="col-actions">
                    <div class="action-buttons">
                        <button class="btn btn-action btn-edit edit-btn" title="Edit rate" data-id="${rate.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-action btn-delete delete-btn" title="Delete rate" data-id="${rate.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    createGridItem(rate) {
        const isSelected = this.selectedRates.has(rate.id);
        const formattedRate = this.formatCurrency(rate.rate);
        return `
            <div class="grid-item ${isSelected ? 'selected' : ''}" data-id="${rate.id}">
                <div class="grid-item-header">
                    <div class="grid-item-rate" title="Exchange rate: 1 USD = ${this.exchangeRates[this.currentCurrency]} ${this.currentCurrency}">${formattedRate}</div>
                    <div class="grid-item-actions">
                        <button class="btn btn-action btn-edit edit-btn" title="Edit rate" data-id="${rate.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-action btn-delete delete-btn" title="Delete rate" data-id="${rate.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="grid-item-date">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Effective from ${this.formatDate(rate.effectiveFrom)}</span>
                </div>
                <div class="grid-item-footer">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} data-id="${rate.id}" aria-label="Select rate">
                    <span>Select for bulk actions</span>
                </div>
            </div>
        `;
    }

    createRateCard(rate) {
        const isSelected = this.selectedRates.has(rate.id);
        const formattedRate = this.formatCurrency(rate.rate);
        const currencyName = this.getCurrencyName(this.currentCurrency);
        return `
            <div class="rate-card ${isSelected ? 'selected' : ''}" data-id="${rate.id}">
                <div class="rate-card-header">
                    <div class="rate-card-info">
                        <div class="rate-card-amount" title="Exchange rate: 1 USD = ${this.exchangeRates[this.currentCurrency]} ${this.currentCurrency}">${formattedRate}</div>
                        <div class="rate-card-currency">${currencyName} per hour</div>
                    </div>
                    <div class="rate-card-actions">
                        <button class="btn btn-action btn-edit edit-btn" title="Edit rate" data-id="${rate.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-action btn-delete delete-btn" title="Delete rate" data-id="${rate.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="rate-card-body">
                    <div class="rate-card-date">
                        <i class="fas fa-calendar-alt"></i>
                        <span class="rate-card-date-label">Effective from</span>
                        <span class="rate-card-date-value">${this.formatDate(rate.effectiveFrom)}</span>
                    </div>
                </div>
                <div class="rate-card-footer">
                    <div class="rate-card-checkbox">
                        <input type="checkbox" ${isSelected ? 'checked' : ''} data-id="${rate.id}" aria-label="Select rate">
                        <span>Select for bulk actions</span>
                    </div>
                </div>
            </div>
        `;
    }

    bindTableEvents() {
        this.bindCommonEvents();
    }

    bindGridEvents() {
        this.bindCommonEvents();
        // Ensure sort buttons are bound for grid view
        this.bindSortableHeaders();
    }

    bindCardEvents() {
        this.bindCommonEvents();
        // Ensure sort buttons are bound for card view
        this.bindSortableHeaders();
    }

    bindCommonEvents() {
        // Edit buttons
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.openEditModal(id);
            });
        });

        // Delete buttons
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.deleteRate(id);
            });
        });

        // Checkboxes
        document.querySelectorAll('input[type="checkbox"][data-id]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const id = parseInt(checkbox.dataset.id);
                if (e.target.checked) {
                    this.selectedRates.add(id);
                } else {
                    this.selectedRates.delete(id);
                }
                this.updateItemSelection(id);
                this.updateBulkActions();
            });
        });
    }

    updateItemSelection(id) {
        // Update selection for all view modes
        const selectors = [
            `tr[data-id="${id}"]`,           // Table row
            `.grid-item[data-id="${id}"]`,   // Grid item
            `.rate-card[data-id="${id}"]`    // Card
        ];

        selectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                if (this.selectedRates.has(id)) {
                    element.classList.add('selected');
                } else {
                    element.classList.remove('selected');
                }
            }
        });
    }

    updateBulkActions() {
        const deleteBtn = document.getElementById('deleteSelectedBtn');
        const selectedCount = document.getElementById('selectedCount');
        const selectAllCheckboxes = [
            document.getElementById('selectAllCheckbox'),
            document.getElementById('selectAllGridCheckbox'),
            document.getElementById('selectAllCardCheckbox')
        ];

        const count = this.selectedRates.size;
        const visibleRates = this.getPaginatedRates();
        const allVisibleSelected = visibleRates.length > 0 && visibleRates.every(rate => this.selectedRates.has(rate.id));

        deleteBtn.disabled = count === 0;
        selectedCount.textContent = `${count} selected`;

        // Update all select all checkboxes state
        selectAllCheckboxes.forEach(checkbox => {
            if (checkbox) {
                checkbox.checked = allVisibleSelected;
                checkbox.indeterminate = count > 0 && !allVisibleSelected;
            }
        });
    }

    handleSelectAll(isChecked) {
        const visibleRates = this.getPaginatedRates();

        if (isChecked) {
            // Select all visible rates
            visibleRates.forEach(rate => {
                this.selectedRates.add(rate.id);
                this.updateItemSelection(rate.id);
            });
        } else {
            // Deselect all visible rates
            visibleRates.forEach(rate => {
                this.selectedRates.delete(rate.id);
                this.updateItemSelection(rate.id);
            });
        }

        // Update individual checkboxes in all view modes
        this.updateAllCheckboxes();
        this.updateBulkActions();
    }

    updateAllCheckboxes() {
        // Update checkboxes in all view modes
        document.querySelectorAll('input[type="checkbox"][data-id]').forEach(checkbox => {
            const id = parseInt(checkbox.dataset.id);
            checkbox.checked = this.selectedRates.has(id);
        });
    }

    renderPagination() {
        const totalPages = this.getTotalPages();
        const filtered = this.getFilteredRates();
        const startIndex = (this.currentPage - 1) * this.pageSize + 1;
        const endIndex = Math.min(this.currentPage * this.pageSize, filtered.length);

        // Update pagination info
        document.getElementById('paginationInfo').textContent =
            `Showing ${startIndex}-${endIndex} of ${filtered.length} rates`;

        // Update pagination buttons
        document.getElementById('firstPageBtn').disabled = this.currentPage === 1;
        document.getElementById('prevPageBtn').disabled = this.currentPage === 1;
        document.getElementById('nextPageBtn').disabled = this.currentPage === totalPages;
        document.getElementById('lastPageBtn').disabled = this.currentPage === totalPages;

        // Update page numbers
        this.renderPageNumbers(totalPages);
    }

    renderPageNumbers(totalPages) {
        const pageNumbers = document.getElementById('pageNumbers');
        pageNumbers.innerHTML = '';

        if (totalPages <= 1) return;

        const maxVisible = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);

        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-number ${i === this.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => this.goToPage(i));
            pageNumbers.appendChild(pageBtn);
        }
    }

    goToPage(page) {
        const totalPages = this.getTotalPages();
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.render();
        }
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        });
    }

    // Modal Management
    openAddModal() {
        document.getElementById('modalTitle').textContent = 'Add Hourly Rate';
        document.getElementById('modalSaveBtn').innerHTML = '<i class="fas fa-save"></i> Save Rate';
        this.clearForm();
        this.showModal();
    }

    openEditModal(id) {
        const rate = this.rates.find(r => r.id === id);
        if (!rate) return;

        document.getElementById('modalTitle').textContent = 'Edit Hourly Rate';
        document.getElementById('modalSaveBtn').innerHTML = '<i class="fas fa-save"></i> Update Rate';

        document.getElementById('rateId').value = rate.id;
        document.getElementById('hourlyRate').value = rate.rate;
        document.getElementById('effectiveFrom').value = rate.effectiveFrom;

        this.showModal();
    }

    showModal() {
        const overlay = document.getElementById('modalOverlay');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Focus the first input
        setTimeout(() => {
            document.getElementById('hourlyRate').focus();
        }, 100);
    }

    closeModal() {
        const overlay = document.getElementById('modalOverlay');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
        this.clearForm();
    }

    clearForm() {
        document.getElementById('rateForm').reset();
        document.getElementById('rateId').value = '';
        this.clearFormErrors();
    }

    clearFormErrors() {
        document.querySelectorAll('.error-message').forEach(el => el.textContent = '');
        document.querySelectorAll('.error').forEach(el => el.classList.remove('error'));
    }

    // Form Validation and Saving
    validateForm() {
        const rate = document.getElementById('hourlyRate').value;
        const effectiveFrom = document.getElementById('effectiveFrom').value;
        let isValid = true;

        this.clearFormErrors();

        // Validate hourly rate
        if (!rate || parseFloat(rate) <= 0) {
            this.showFieldError('hourlyRate', 'Please enter a valid hourly rate greater than 0');
            isValid = false;
        } else if (parseFloat(rate) > 10000) {
            this.showFieldError('hourlyRate', 'Hourly rate cannot exceed $10,000');
            isValid = false;
        }

        // Validate effective date
        if (!effectiveFrom) {
            this.showFieldError('effectiveFrom', 'Please select an effective date');
            isValid = false;
        } else {
            const selectedDate = new Date(effectiveFrom);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate > today) {
                // Allow future dates but show a warning
                this.showFieldWarning('effectiveFrom', 'This date is in the future');
            }
        }

        return isValid;
    }

    showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorElement = document.getElementById(fieldId + 'Error');

        field.classList.add('error');
        errorElement.textContent = message;
    }

    showFieldWarning(fieldId, message) {
        const errorElement = document.getElementById(fieldId + 'Error');
        errorElement.textContent = message;
        errorElement.style.color = 'var(--warning-color)';
    }

    saveRate() {
        if (!this.validateForm()) return;

        const rateId = document.getElementById('rateId').value;
        const rate = parseFloat(document.getElementById('hourlyRate').value);
        const effectiveFrom = document.getElementById('effectiveFrom').value;

        if (rateId) {
            // Update existing rate
            const index = this.rates.findIndex(r => r.id === parseInt(rateId));
            if (index !== -1) {
                this.rates[index] = { ...this.rates[index], rate, effectiveFrom };
                this.showToast('success', 'Rate Updated', 'Hourly rate has been updated successfully');
            }
        } else {
            // Add new rate
            const newRate = {
                id: this.getNextId(),
                rate,
                effectiveFrom
            };
            this.rates.push(newRate);
            this.showToast('success', 'Rate Added', 'New hourly rate has been added successfully');
        }

        this.saveToStorage();
        this.closeModal();
        this.render();
    }

    getNextId() {
        return this.rates.length > 0 ? Math.max(...this.rates.map(r => r.id)) + 1 : 1;
    }

    // Delete Operations
    deleteRate(id) {
        const rate = this.rates.find(r => r.id === id);
        if (!rate) return;

        const formattedRate = this.formatCurrency(rate.rate);
        this.showConfirmModal(
            'Delete Rate',
            `Are you sure you want to delete the rate of ${formattedRate}?`,
            () => {
                this.rates = this.rates.filter(r => r.id !== id);
                this.selectedRates.delete(id);
                this.saveToStorage();
                this.render();
                this.showToast('success', 'Rate Deleted', 'Hourly rate has been deleted successfully');
            }
        );
    }

    deleteSelected() {
        if (this.selectedRates.size === 0) return;

        const count = this.selectedRates.size;
        this.showConfirmModal(
            'Delete Selected Rates',
            `Are you sure you want to delete ${count} selected rate${count > 1 ? 's' : ''}?`,
            () => {
                this.rates = this.rates.filter(r => !this.selectedRates.has(r.id));
                this.selectedRates.clear();
                this.saveToStorage();
                this.render();
                this.showToast('success', 'Rates Deleted', `${count} rate${count > 1 ? 's have' : ' has'} been deleted successfully`);
            }
        );
    }

    // Confirmation Modal
    showConfirmModal(title, message, onConfirm) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmMessage').textContent = message;

        const overlay = document.getElementById('confirmModalOverlay');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Remove any existing event listeners
        const confirmBtn = document.getElementById('confirmOkBtn');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        newConfirmBtn.addEventListener('click', () => {
            onConfirm();
            this.closeConfirmModal();
        });
    }

    closeConfirmModal() {
        const overlay = document.getElementById('confirmModalOverlay');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    // Toast Notifications
    showToast(type, title, message) {
        const toastContainer = document.getElementById('toastContainer');
        const toastId = 'toast-' + Date.now();

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.id = toastId;
        toast.innerHTML = `
            <i class="toast-icon ${iconMap[type] || iconMap.info}"></i>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        toastContainer.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
    }

    // Data Persistence
    saveToStorage() {
        try {
            localStorage.setItem('hourlyRates', JSON.stringify(this.rates));
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
            this.showToast('error', 'Save Failed', 'Failed to save data to local storage');
        }
    }

    loadFromStorage() {
        try {
            const stored = localStorage.getItem('hourlyRates');
            if (stored) {
                this.rates = JSON.parse(stored);
            }

            // Load view mode preference
            const storedViewMode = localStorage.getItem('hourlyRatesViewMode');
            if (storedViewMode && ['list', 'grid', 'card'].includes(storedViewMode)) {
                this.currentView = storedViewMode;
                this.updateViewModeButtons();
                this.switchViewContainer();
            }

            // Load theme preference
            const storedTheme = localStorage.getItem('hourlyRatesTheme');
            if (storedTheme && ['white', 'dark', 'blue', 'green', 'purple'].includes(storedTheme)) {
                this.currentTheme = storedTheme;
                this.applyTheme(storedTheme);
            }

            // Load font preference
            const storedFont = localStorage.getItem('hourlyRatesFont');
            if (storedFont && ['times', 'arial', 'helvetica', 'georgia', 'verdana'].includes(storedFont)) {
                this.currentFont = storedFont;
                this.applyFont(storedFont);
            }

            // Load sort preference
            const storedSort = localStorage.getItem('hourlyRatesSort');
            if (storedSort) {
                try {
                    const sortState = JSON.parse(storedSort);
                    if (sortState && sortState.column && sortState.direction) {
                        this.currentSort = sortState;
                        // Update indicators after DOM is ready
                        setTimeout(() => this.updateSortIndicators(), 100);
                    }
                } catch (error) {
                    console.error('Failed to parse stored sort state:', error);
                }
            }

            // Load currency preference
            const storedCurrency = localStorage.getItem('hourlyRatesCurrency');
            if (storedCurrency && this.exchangeRates[storedCurrency]) {
                this.currentCurrency = storedCurrency;
                // Update button display after DOM is ready
                setTimeout(() => {
                    document.getElementById('currentCurrency').textContent = storedCurrency;
                    this.updateCurrencyState();
                }, 100);
            }
        } catch (error) {
            console.error('Failed to load from localStorage:', error);
            this.showToast('error', 'Load Failed', 'Failed to load data from local storage');
        }
    }

    saveViewModeToStorage() {
        try {
            localStorage.setItem('hourlyRatesViewMode', this.currentView);
        } catch (error) {
            console.error('Failed to save view mode to localStorage:', error);
        }
    }

    // Settings Panel Management
    bindSettingsEvents() {
        // Theme options
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', () => {
                const theme = option.dataset.theme;
                this.setTheme(theme);
            });
        });

        // View mode options in settings
        document.querySelectorAll('.view-mode-option').forEach(option => {
            option.addEventListener('click', () => {
                const view = option.dataset.view;
                this.setViewMode(view);
                this.updateSettingsViewMode();
            });
        });

        // Font options
        document.querySelectorAll('.font-option').forEach(option => {
            option.addEventListener('click', () => {
                const font = option.dataset.font;
                this.setFont(font);
            });
        });
    }

    toggleSettingsPanel() {
        const settingsContainer = document.querySelector('.settings-container');
        const settingsPanel = document.getElementById('settingsPanel');

        if (settingsPanel.classList.contains('active')) {
            this.closeSettingsPanel();
        } else {
            this.openSettingsPanel();
        }
    }

    openSettingsPanel() {
        const settingsContainer = document.querySelector('.settings-container');
        const settingsPanel = document.getElementById('settingsPanel');

        // Lazy bind events if not already bound
        if (!this.settingsEventsBound) {
            this.bindSettingsEvents();
            this.settingsEventsBound = true;
        }

        settingsContainer.classList.add('open');
        settingsPanel.classList.add('active');
        settingsPanel.setAttribute('aria-hidden', 'false');

        // Update settings to reflect current state
        this.updateSettingsState();

        // Focus management
        setTimeout(() => {
            const firstOption = settingsPanel.querySelector('.theme-option');
            if (firstOption) firstOption.focus();
        }, 100);
    }

    closeSettingsPanel() {
        const settingsContainer = document.querySelector('.settings-container');
        const settingsPanel = document.getElementById('settingsPanel');

        settingsContainer.classList.remove('open');
        settingsPanel.classList.remove('active');
        settingsPanel.setAttribute('aria-hidden', 'true');
    }

    updateSettingsState() {
        // Update theme selection
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.toggle('active', option.dataset.theme === this.currentTheme);
        });

        // Update view mode selection
        this.updateSettingsViewMode();

        // Update font selection
        document.querySelectorAll('.font-option').forEach(option => {
            option.classList.toggle('active', option.dataset.font === this.currentFont);
        });
    }

    updateSettingsViewMode() {
        document.querySelectorAll('.view-mode-option').forEach(option => {
            option.classList.toggle('active', option.dataset.view === this.currentView);
        });
    }

    // Theme Management
    setTheme(theme) {
        this.currentTheme = theme;
        this.applyTheme(theme);
        this.updateSettingsState();
        this.saveThemeToStorage();
        this.showToast('success', 'Theme Changed', `Switched to ${theme} theme`);
    }

    applyTheme(theme) {
        const root = document.documentElement;

        // Remove existing theme classes
        document.body.classList.remove('theme-white', 'theme-dark', 'theme-blue', 'theme-green', 'theme-purple');

        // Apply new theme
        document.body.classList.add(`theme-${theme}`);

        // Update CSS custom properties based on theme
        switch (theme) {
            case 'white':
                root.style.setProperty('--primary-color', '#3b82f6');
                root.style.setProperty('--background-color', '#f8fafc');
                root.style.setProperty('--surface-color', '#ffffff');
                break;
            case 'dark':
                root.style.setProperty('--primary-color', '#60a5fa');
                root.style.setProperty('--background-color', '#1f2937');
                root.style.setProperty('--surface-color', '#374151');
                break;
            case 'blue':
                root.style.setProperty('--primary-color', '#2563eb');
                root.style.setProperty('--background-color', '#eff6ff');
                root.style.setProperty('--surface-color', '#ffffff');
                break;
            case 'green':
                root.style.setProperty('--primary-color', '#10b981');
                root.style.setProperty('--background-color', '#ecfdf5');
                root.style.setProperty('--surface-color', '#ffffff');
                break;
            case 'purple':
                root.style.setProperty('--primary-color', '#8b5cf6');
                root.style.setProperty('--background-color', '#faf5ff');
                root.style.setProperty('--surface-color', '#ffffff');
                break;
        }
    }

    // Font Management
    setFont(font) {
        this.currentFont = font;
        this.applyFont(font);
        this.updateSettingsState();
        this.saveFontToStorage();
        this.showToast('success', 'Font Changed', `Switched to ${font} font`);
    }

    applyFont(font) {
        const fontFamilies = {
            times: "'Times New Roman', serif",
            arial: "Arial, sans-serif",
            helvetica: "'Helvetica Neue', sans-serif",
            georgia: "Georgia, serif",
            verdana: "Verdana, sans-serif"
        };

        document.body.style.fontFamily = fontFamilies[font] || fontFamilies.times;
    }

    // Storage for settings
    saveThemeToStorage() {
        try {
            localStorage.setItem('hourlyRatesTheme', this.currentTheme);
        } catch (error) {
            console.error('Failed to save theme to localStorage:', error);
        }
    }

    saveFontToStorage() {
        try {
            localStorage.setItem('hourlyRatesFont', this.currentFont);
        } catch (error) {
            console.error('Failed to save font to localStorage:', error);
        }
    }

    // Sortable Headers Management
    bindSortableHeaders() {
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                const column = header.dataset.sort;
                this.handleSort(column);
            });

            header.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    const column = header.dataset.sort;
                    this.handleSort(column);
                }
            });
        });
    }

    handleSort(column) {
        // Determine new sort direction
        let newDirection;

        if (this.currentSort.column === column) {
            // Same column clicked - cycle through directions
            if (this.currentSort.direction === 'asc') {
                newDirection = 'desc';
            } else if (this.currentSort.direction === 'desc') {
                newDirection = null; // Reset to unsorted
            } else {
                newDirection = 'asc';
            }
        } else {
            // Different column clicked - start with ascending
            newDirection = 'asc';
        }

        // Update sort state
        this.currentSort = {
            column: newDirection ? column : null,
            direction: newDirection
        };

        // Update visual indicators
        this.updateSortIndicators();

        // Reset to first page when sorting
        this.currentPage = 1;

        // Re-render data
        this.render();

        // Save sort state
        this.saveSortToStorage();

        // Announce sort change for screen readers
        this.announceSortChange(column, newDirection);
    }

    updateSortIndicators() {
        // Reset all headers
        document.querySelectorAll('.sortable').forEach(header => {
            header.classList.remove('sort-asc', 'sort-desc');
            header.setAttribute('aria-sort', 'none');
        });

        // Update active header
        if (this.currentSort.column) {
            const activeHeader = document.querySelector(`[data-sort="${this.currentSort.column}"]`);
            if (activeHeader) {
                activeHeader.classList.add(`sort-${this.currentSort.direction}`);
                activeHeader.setAttribute('aria-sort', this.currentSort.direction === 'asc' ? 'ascending' : 'descending');

                // Update aria-label for better accessibility
                const columnName = this.currentSort.column === 'rate' ? 'hourly rate' : 'effective date';
                const directionText = this.currentSort.direction === 'asc' ? 'ascending' : 'descending';
                activeHeader.setAttribute('aria-label', `Sort by ${columnName}, currently sorted ${directionText}. Click to change sort order.`);
            }
        }
    }

    announceSortChange(column, direction) {
        // Create a live region announcement for screen readers
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.style.position = 'absolute';
        announcement.style.left = '-10000px';
        announcement.style.width = '1px';
        announcement.style.height = '1px';
        announcement.style.overflow = 'hidden';

        const columnName = column === 'rate' ? 'Hourly Rate' : 'Effective Date';
        if (direction) {
            const directionText = direction === 'asc' ? 'ascending' : 'descending';
            announcement.textContent = `Table sorted by ${columnName} in ${directionText} order`;
        } else {
            announcement.textContent = 'Table sort cleared, returned to original order';
        }

        document.body.appendChild(announcement);

        // Remove the announcement after it's been read
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    // Storage for sort state
    saveSortToStorage() {
        try {
            localStorage.setItem('hourlyRatesSort', JSON.stringify(this.currentSort));
        } catch (error) {
            console.error('Failed to save sort state to localStorage:', error);
        }
    }

    // Currency Management
    bindCurrencyEvents() {
        // Currency options
        document.querySelectorAll('.currency-option').forEach(option => {
            option.addEventListener('click', () => {
                const currency = option.dataset.currency;
                this.setCurrency(currency);
            });
        });
    }

    toggleCurrencyPanel() {
        const currencyContainer = document.querySelector('.currency-container');
        const currencyPanel = document.getElementById('currencyPanel');

        if (currencyPanel.classList.contains('active')) {
            this.closeCurrencyPanel();
        } else {
            this.openCurrencyPanel();
        }
    }

    openCurrencyPanel() {
        const currencyContainer = document.querySelector('.currency-container');
        const currencyPanel = document.getElementById('currencyPanel');

        // Close settings panel if open
        this.closeSettingsPanel();

        currencyContainer.classList.add('open');
        currencyPanel.classList.add('active');
        currencyPanel.setAttribute('aria-hidden', 'false');

        // Update currency options to reflect current state
        this.updateCurrencyState();

        // Focus management
        setTimeout(() => {
            const firstOption = currencyPanel.querySelector('.currency-option');
            if (firstOption) firstOption.focus();
        }, 100);
    }

    closeCurrencyPanel() {
        const currencyContainer = document.querySelector('.currency-container');
        const currencyPanel = document.getElementById('currencyPanel');

        currencyContainer.classList.remove('open');
        currencyPanel.classList.remove('active');
        currencyPanel.setAttribute('aria-hidden', 'true');
    }

    updateCurrencyState() {
        // Update currency selection
        document.querySelectorAll('.currency-option').forEach(option => {
            option.classList.toggle('active', option.dataset.currency === this.currentCurrency);
        });
    }

    setCurrency(currency) {
        if (this.currentCurrency === currency) {
            this.closeCurrencyPanel();
            return;
        }

        this.currentCurrency = currency;

        // Update button display
        document.getElementById('currentCurrency').textContent = currency;

        // Update currency state
        this.updateCurrencyState();

        // Re-render all data with new currency
        this.render();

        // Save currency preference
        this.saveCurrencyToStorage();

        // Close panel
        this.closeCurrencyPanel();

        // Announce currency change for screen readers
        this.announceCurrencyChange(currency);

        // Show toast notification
        const currencyName = this.getCurrencyName(currency);
        this.showToast('success', 'Currency Changed', `Switched to ${currencyName} (${this.currencySymbols[currency]})`);
    }

    getCurrencyName(currency) {
        const names = {
            'USD': 'US Dollar',
            'INR': 'Indian Rupee',
            'EUR': 'Euro',
            'GBP': 'British Pound',
            'CAD': 'Canadian Dollar',
            'AUD': 'Australian Dollar'
        };
        return names[currency] || currency;
    }

    convertCurrency(amount, fromCurrency = this.baseCurrency, toCurrency = this.currentCurrency) {
        if (fromCurrency === toCurrency) return amount;

        // Create cache key
        const cacheKey = `${amount}_${fromCurrency}_${toCurrency}`;

        // Check cache first
        if (this.conversionCache.has(cacheKey)) {
            return this.conversionCache.get(cacheKey);
        }

        // Convert to USD first if not already
        let usdAmount = amount;
        if (fromCurrency !== 'USD') {
            usdAmount = amount / this.exchangeRates[fromCurrency];
        }

        // Convert from USD to target currency
        let result = usdAmount;
        if (toCurrency !== 'USD') {
            result = usdAmount * this.exchangeRates[toCurrency];
        }

        // Cache the result
        this.conversionCache.set(cacheKey, result);

        // Limit cache size to prevent memory issues
        if (this.conversionCache.size > 1000) {
            const firstKey = this.conversionCache.keys().next().value;
            this.conversionCache.delete(firstKey);
        }

        return result;
    }

    formatCurrency(amount, currency = this.currentCurrency) {
        const convertedAmount = this.convertCurrency(amount, this.baseCurrency, currency);
        const symbol = this.currencySymbols[currency];

        // Format based on currency
        let formattedAmount;
        if (currency === 'INR') {
            // Indian number format with commas
            formattedAmount = convertedAmount.toLocaleString('en-IN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        } else {
            // Standard format for other currencies
            formattedAmount = convertedAmount.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        return `${symbol}${formattedAmount}`;
    }

    announceCurrencyChange(currency) {
        // Create a live region announcement for screen readers
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.style.position = 'absolute';
        announcement.style.left = '-10000px';
        announcement.style.width = '1px';
        announcement.style.height = '1px';
        announcement.style.overflow = 'hidden';

        const currencyName = this.getCurrencyName(currency);
        const symbol = this.currencySymbols[currency];
        announcement.textContent = `Currency changed to ${currencyName}. All rates are now displayed in ${symbol} ${currency}.`;

        document.body.appendChild(announcement);

        // Remove the announcement after it's been read
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    // Storage for currency
    saveCurrencyToStorage() {
        try {
            localStorage.setItem('hourlyRatesCurrency', this.currentCurrency);
        } catch (error) {
            console.error('Failed to save currency to localStorage:', error);
        }
    }

    // Utility Methods
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Cache DOM elements for better performance
    cacheElements() {
        this.elements = {
            tableBody: document.getElementById('tableBody'),
            gridContainer: document.getElementById('gridContainer'),
            cardsContainer: document.getElementById('cardsContainer'),
            paginationInfo: document.getElementById('paginationInfo'),
            pageNumbers: document.getElementById('pageNumbers'),
            selectedCount: document.getElementById('selectedCount'),
            deleteSelectedBtn: document.getElementById('deleteSelectedBtn'),
            currentCurrency: document.getElementById('currentCurrency'),
            settingsPanel: document.getElementById('settingsPanel'),
            currencyPanel: document.getElementById('currencyPanel')
        };
    }

    refresh() {
        this.selectedRates.clear();
        this.currentPage = 1;
        this.searchTerm = '';
        document.getElementById('searchInput').value = '';
        this.render();
        this.updateBulkActions(); // Update select all checkboxes
        this.showToast('info', 'Refreshed', 'Data has been refreshed');
    }

    // Export functionality (bonus feature)
    exportToCSV() {
        const headers = ['Hourly Rate', 'Effective From'];
        const csvContent = [
            headers.join(','),
            ...this.rates.map(rate => `${rate.rate},${rate.effectiveFrom}`)
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'hourly-rates.csv';
        a.click();
        window.URL.revokeObjectURL(url);

        this.showToast('success', 'Export Complete', 'Data has been exported to CSV');
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.hourlyRateManager = new HourlyRateManager();
});

// Global functions for HTML event handlers
function openAddModal() {
    window.hourlyRateManager.openAddModal();
}
