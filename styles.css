/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --primary-light: #dbeafe;
    --secondary-color: #6b7280;
    --secondary-hover: #4b5563;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --warning-color: #f59e0b;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --border-color: #e5e7eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
}

.container {
    width: 100%;
    max-width: none;
    margin: 0;
    padding: var(--spacing-md) var(--spacing-lg);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--border-color);
}

.header-content {
    padding: var(--spacing-lg) var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.app-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.app-title i {
    color: var(--primary-color);
    font-size: 1.75rem;
}

.header-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
    min-height: 2.5rem;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--background-color);
    border-color: var(--secondary-color);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: var(--danger-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-icon {
    padding: var(--spacing-sm);
    min-width: 2.5rem;
    justify-content: center;
}

/* Controls Section */
.controls {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-lg) var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.search-container {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
    align-items: center;
    flex: 1;
}

.controls-right {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.search-box input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.filter-controls select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--surface-color);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-controls select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.selected-count {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* View Mode Selector */
.view-mode-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.view-mode-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    white-space: nowrap;
}

.view-mode-buttons {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
    background: var(--surface-color);
}

.btn-view-mode {
    padding: var(--spacing-sm);
    border: none;
    background: var(--surface-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 2.5rem;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid var(--border-color);
}

.btn-view-mode:last-child {
    border-right: none;
}

.btn-view-mode:hover {
    background: var(--primary-light);
    color: var(--primary-color);
}

.btn-view-mode.active {
    background: var(--primary-color);
    color: white;
}

.btn-view-mode:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Settings Container and Panel */
.settings-container {
    position: relative;
    display: inline-block;
}

.settings-chevron {
    margin-left: var(--spacing-xs);
    font-size: 0.75rem;
    transition: transform var(--transition-fast);
}

.settings-container.open .settings-chevron {
    transform: rotate(180deg);
}

.settings-panel {
    position: absolute;
    top: calc(100% + var(--spacing-sm));
    right: 0;
    width: 280px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    max-height: 80vh;
    overflow-y: auto;
}

.settings-panel.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.settings-content {
    padding: var(--spacing-md);
}

.settings-section {
    margin-bottom: var(--spacing-lg);
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.settings-section-header i {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.settings-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-muted);
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.settings-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

/* Theme Options */
.theme-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.theme-option:hover {
    background: var(--primary-light);
}

.theme-option.active {
    background: var(--primary-color);
    color: white;
}

.theme-color {
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid var(--border-color);
    flex-shrink: 0;
}

.theme-color.white {
    background: #ffffff;
}

.theme-color.dark {
    background: #1f2937;
}

.theme-color.blue {
    background: var(--primary-color);
}

.theme-color.green {
    background: #10b981;
}

.theme-color.purple {
    background: #8b5cf6;
}

.theme-option span {
    flex: 1;
    font-size: 0.875rem;
    font-weight: 500;
}

.theme-check {
    opacity: 0;
    transition: opacity var(--transition-fast);
    font-size: 0.875rem;
}

.theme-option.active .theme-check {
    opacity: 1;
}

/* View Mode Options */
.view-mode-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.view-mode-option:hover {
    background: var(--primary-light);
}

.view-mode-option.active {
    background: var(--primary-color);
    color: white;
}

.view-mode-option i {
    width: 1rem;
    text-align: center;
    flex-shrink: 0;
}

.view-mode-option span {
    flex: 1;
    font-size: 0.875rem;
    font-weight: 500;
}

.view-check {
    opacity: 0;
    transition: opacity var(--transition-fast);
    font-size: 0.875rem;
}

.view-mode-option.active .view-check {
    opacity: 1;
}

/* Font Options */
.font-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.font-option:hover {
    background: var(--primary-light);
}

.font-option.active {
    background: var(--primary-color);
    color: white;
}

.font-option span {
    font-size: 0.875rem;
    font-weight: 500;
}

.font-check {
    opacity: 0;
    transition: opacity var(--transition-fast);
    font-size: 0.875rem;
}

.font-option.active .font-check {
    opacity: 1;
}

/* Currency Container and Panel */
.currency-container {
    position: relative;
    display: inline-block;
}

.currency-chevron {
    margin-left: var(--spacing-xs);
    font-size: 0.75rem;
    transition: transform var(--transition-fast);
}

.currency-container.open .currency-chevron {
    transform: rotate(180deg);
}

.currency-panel {
    position: absolute;
    top: calc(100% + var(--spacing-sm));
    right: 0;
    width: 300px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    max-height: 80vh;
    overflow-y: auto;
}

.currency-panel.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.currency-content {
    padding: var(--spacing-md);
}

.currency-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.currency-header i {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.currency-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-muted);
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.currency-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.currency-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.currency-option:hover {
    background: var(--primary-light);
}

.currency-option.active {
    background: var(--primary-color);
    color: white;
}

.currency-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.currency-symbol {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    flex-shrink: 0;
}

.currency-option.active .currency-symbol {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.currency-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.currency-code {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1;
}

.currency-name {
    font-size: 0.75rem;
    opacity: 0.8;
    line-height: 1;
}

.currency-check {
    opacity: 0;
    transition: opacity var(--transition-fast);
    font-size: 0.875rem;
}

.currency-option.active .currency-check {
    opacity: 1;
}

.currency-footer {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.exchange-rate-note {
    color: var(--text-muted);
    font-size: 0.75rem;
    line-height: 1.3;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-bottom: var(--spacing-xl);
    width: 100%;
}

/* View Container Management */
.view-container {
    display: none;
    width: 100%;
}

.view-container.active {
    display: block;
}

/* List View (Table) Styles */
.table-container {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
    width: 100%;
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    table-layout: fixed;
    min-width: 700px;
}

.data-table th {
    background-color: var(--background-color);
    color: var(--text-primary);
    font-weight: 600;
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.data-table th:last-child {
    border-right: none;
}

/* Sortable Column Headers */
.data-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: all var(--transition-fast);
    position: relative;
}

.data-table th.sortable:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.data-table th.sortable:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

.sort-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--spacing-sm);
}

.sort-icon {
    position: relative;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.sort-icon i {
    position: absolute;
    font-size: 0.75rem;
    transition: opacity var(--transition-fast);
    opacity: 0;
}

/* Default state - show neutral sort icon */
.sortable .sort-neutral {
    opacity: 0.5;
}

.sortable:hover .sort-neutral {
    opacity: 0.8;
}

/* Active sort states */
.sortable.sort-asc .sort-neutral,
.sortable.sort-desc .sort-neutral {
    opacity: 0;
}

.sortable.sort-asc .sort-asc {
    opacity: 1;
    color: var(--primary-color);
}

.sortable.sort-desc .sort-desc {
    opacity: 1;
    color: var(--primary-color);
}

/* Hover states for active sorts */
.sortable.sort-asc:hover .sort-asc,
.sortable.sort-desc:hover .sort-desc {
    opacity: 1;
    color: var(--primary-hover);
}

.data-table td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    box-sizing: border-box;
}

.data-table td:last-child {
    border-right: none;
}

/* Ensure proper column alignment */
.data-table th,
.data-table td {
    box-sizing: border-box;
}

.data-table th.col-select,
.data-table td.col-select {
    text-align: center;
    vertical-align: middle;
}

.data-table th.col-rate,
.data-table td.col-rate {
    text-align: left;
    vertical-align: middle;
}

.data-table th.col-date,
.data-table td.col-date {
    text-align: left;
    vertical-align: middle;
}

.data-table th.col-actions,
.data-table td.col-actions {
    text-align: center;
    vertical-align: middle;
}

.data-table tbody tr:nth-child(even) {
    background-color: var(--background-color);
}

.data-table tbody tr:hover {
    background-color: var(--primary-light);
}

.data-table tbody tr.selected {
    background-color: rgba(59, 130, 246, 0.1);
    border-left: 3px solid var(--primary-color);
}

/* Column Widths */
.col-select {
    width: 60px;
    min-width: 60px;
    text-align: center;
}

.col-rate {
    width: 180px;
    min-width: 180px;
    text-align: left;
}

.col-date {
    width: 160px;
    min-width: 160px;
    text-align: left;
}

.col-actions {
    width: 140px;
    min-width: 140px;
    text-align: center;
}

/* Action buttons container */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
    align-items: center;
}

/* Table cell content styling */
.rate-amount {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.rate-date {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Improve table row spacing */
.data-table tbody tr {
    height: 3.5rem;
}

/* Better hover effects */
.data-table tbody tr:hover .rate-amount {
    color: var(--primary-hover);
}

/* Grid View Styles */
.grid-view-header {
    padding: var(--spacing-md) var(--spacing-sm);
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.grid-sort-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.grid-select-all {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.grid-select-all input[type="checkbox"] {
    width: 1.125rem;
    height: 1.125rem;
    accent-color: var(--primary-color);
    cursor: pointer;
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    width: 100%;
}

.grid-item {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.grid-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.grid-item.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.grid-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.grid-item-rate {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.grid-item-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.grid-item-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.grid-item-date i {
    color: var(--text-muted);
}

.grid-item-footer {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

/* Card View Styles */
.card-view-header {
    padding: var(--spacing-md) var(--spacing-sm);
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    border-bottom: none;
    margin-bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.card-sort-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.card-select-all {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.card-select-all input[type="checkbox"] {
    width: 1.125rem;
    height: 1.125rem;
    accent-color: var(--primary-color);
    cursor: pointer;
}

.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    width: 100%;
}

/* Sort Controls Common Styles */
.sort-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    white-space: nowrap;
}

.btn-sort {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    user-select: none;
}

.btn-sort:hover {
    background: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-sort:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn-sort.sort-asc,
.btn-sort.sort-desc {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-sort.sort-asc:hover,
.btn-sort.sort-desc:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

/* Rate Card Styles */
.rate-card {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 200px;
}

.rate-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.rate-card.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.rate-card-header {
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.rate-card-info {
    flex: 1;
}

.rate-card-amount {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rate-card-currency {
    font-size: 0.875rem;
    opacity: 0.9;
    font-weight: 500;
}

.rate-card-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.rate-card-body {
    padding: var(--spacing-md);
    flex: 1;
}

.rate-card-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    font-size: 1rem;
    color: var(--text-secondary);
}

.rate-card-date i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.rate-card-date-label {
    font-weight: 500;
}

.rate-card-date-value {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.125rem;
}

.rate-card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--background-color);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.rate-card-checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
}

.rate-card-checkbox input[type="checkbox"] {
    width: 1.125rem;
    height: 1.125rem;
    accent-color: var(--primary-color);
    cursor: pointer;
}

/* Action Button Styles */
.btn-action {
    padding: var(--spacing-xs);
    min-height: 2rem;
    min-width: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.btn-edit {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-edit:hover {
    background-color: var(--primary-hover);
    transform: scale(1.05);
}

.btn-delete {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.btn-delete:hover {
    background-color: var(--danger-hover);
    transform: scale(1.05);
}



/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.empty-state i {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

/* Pagination Styles */
.pagination-container {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-lg) var(--spacing-xl);
    border: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.page-numbers {
    display: flex;
    gap: var(--spacing-xs);
}

.page-number {
    min-width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
    background: var(--surface-color);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.page-number:hover {
    background: var(--background-color);
    border-color: var(--primary-color);
}

.page-number.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.page-size-selector select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface-color);
    font-size: 0.875rem;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    padding: var(--spacing-lg);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all var(--transition-normal);
}

.modal-overlay.active .modal {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    color: var(--text-muted);
}

.modal-close:hover {
    color: var(--text-primary);
    background: var(--background-color);
}

.modal-body {
    padding: var(--spacing-xl);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

.confirm-modal {
    max-width: 400px;
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.form-group label.required::after {
    content: ' *';
    color: var(--danger-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    background: var(--surface-color);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-group {
    position: relative;
    display: flex;
}

.input-prefix {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-right: none;
    border-radius: var(--radius-md) 0 0 var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.input-group input {
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
    border-left: none;
}

.input-group input:focus {
    box-shadow: 0 0 0 3px var(--primary-light);
}

.error-message {
    margin-top: var(--spacing-xs);
    font-size: 0.75rem;
    color: var(--danger-color);
    font-weight: 500;
    min-height: 1rem;
}

.form-help {
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-top: var(--spacing-xs);
    display: block;
    line-height: 1.3;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    border-left: 4px solid var(--primary-color);
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    transition: all var(--transition-normal);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--danger-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast-icon {
    font-size: 1.125rem;
    margin-top: 0.125rem;
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--danger-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.toast-message {
    font-size: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    font-size: 1rem;
    transition: color var(--transition-fast);
}

.toast-close:hover {
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .header-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .header-actions {
        justify-content: center;
    }

    .controls {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .search-container {
        flex-direction: column;
        align-items: stretch;
    }

    .controls-right {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .view-mode-selector {
        justify-content: center;
    }

    .search-box {
        min-width: auto;
    }

    /* List View Responsive */
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .data-table {
        min-width: 650px;
    }

    .data-table th,
    .data-table td {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .col-select {
        width: 50px;
        min-width: 50px;
    }

    .col-rate {
        width: 150px;
        min-width: 150px;
    }

    .col-date {
        width: 130px;
        min-width: 130px;
    }

    .col-actions {
        width: 120px;
        min-width: 120px;
    }

    /* Grid View Responsive */
    .grid-sort-controls,
    .card-sort-controls {
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .btn-sort {
        font-size: 0.75rem;
        padding: var(--spacing-xs);
    }

    .grid-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    /* Card View Responsive */
    .cards-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .rate-card-amount {
        font-size: 1.875rem;
    }

    .pagination-container {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }

    .modal {
        margin: var(--spacing-md);
        max-width: none;
    }

    .toast-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
    }

    .toast {
        min-width: auto;
        max-width: none;
    }

    /* Settings Panel Responsive */
    .settings-panel {
        position: fixed;
        top: 50%;
        left: 50%;
        right: auto;
        transform: translate(-50%, -50%);
        width: calc(100vw - 2rem);
        max-width: 320px;
        max-height: 80vh;
    }

    .settings-panel.active {
        transform: translate(-50%, -50%);
    }

    /* Currency Panel Responsive */
    .currency-panel {
        position: fixed;
        top: 50%;
        left: 50%;
        right: auto;
        transform: translate(-50%, -50%);
        width: calc(100vw - 2rem);
        max-width: 340px;
        max-height: 80vh;
    }

    .currency-panel.active {
        transform: translate(-50%, -50%);
    }
}

@media (max-width: 480px) {
    .app-title {
        font-size: 1.5rem;
    }

    .btn {
        font-size: 0.75rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .rate-amount {
        font-size: 1.5rem;
    }

    .page-numbers {
        display: none;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn var(--transition-normal) ease-in-out;
}

.slide-up {
    animation: slideUp var(--transition-normal) ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Focus Styles for Accessibility */
.btn:focus-visible,
input:focus-visible,
select:focus-visible,
.rate-card:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Theme Variations */
.theme-dark {
    --background-color: #1f2937;
    --surface-color: #374151;
    --border-color: #4b5563;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --table-stripe: #4b5563;
    --table-hover: #6b7280;
}

.theme-blue {
    --background-color: #eff6ff;
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
}

.theme-green {
    --background-color: #ecfdf5;
    --primary-color: #10b981;
    --primary-hover: #059669;
    --primary-light: #d1fae5;
}

.theme-purple {
    --background-color: #faf5ff;
    --primary-color: #8b5cf6;
    --primary-hover: #7c3aed;
    --primary-light: #e9d5ff;
}
