# Hourly Rate Manager

A modern, responsive web application for managing hourly rates with an intuitive user interface and enhanced functionality.

## Features

### Core Functionality
- ✅ **Add/Edit/Delete** hourly rates
- ✅ **Bulk operations** for managing multiple rates
- ✅ **Search and filter** capabilities
- ✅ **Pagination** with customizable page sizes
- ✅ **Data persistence** using localStorage
- ✅ **Form validation** with real-time feedback

### Enhanced User Experience
- 🎨 **Modern UI Design** with clean, professional styling
- 📱 **Responsive Layout** that works on desktop and mobile
- ♿ **Accessibility Features** including keyboard navigation and screen reader support
- 🔔 **Toast Notifications** for user feedback
- ⚡ **Smooth Animations** and transitions
- 🌙 **High Contrast Mode** support
- ♿ **Reduced Motion** support for accessibility

### Technical Features
- 🏗️ **Vanilla JavaScript** - No framework dependencies
- 💾 **Local Storage** for data persistence
- 🔍 **Real-time Search** with instant filtering
- 📊 **Multiple Sort Options** (by date or rate)
- 📄 **CSV Export** functionality
- 🎯 **Modern CSS** with CSS Grid and Flexbox
- 🎨 **CSS Custom Properties** for consistent theming

## Getting Started

### Prerequisites
- A modern web browser (Chrome, Firefox, Safari, Edge)
- No server setup required - runs entirely in the browser

### Installation
1. Download or clone the project files
2. Open `index.html` in your web browser
3. Start managing your hourly rates!

### File Structure
```
hourly-rate-manager/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS styling
├── script.js           # JavaScript functionality
└── README.md           # This documentation
```

## Usage

### Adding a New Rate
1. Click the "Add Rate" button in the header
2. Enter the hourly rate (must be greater than $0)
3. Select the effective date
4. Click "Save Rate"

### Editing an Existing Rate
1. Click the edit icon (pencil) on any rate card
2. Modify the rate or date as needed
3. Click "Update Rate"

### Deleting Rates
- **Single Delete**: Click the delete icon (trash) on any rate card
- **Bulk Delete**: Select multiple rates using checkboxes, then click "Delete Selected"

### Search and Filter
- Use the search box to find rates by amount or date
- Use the sort dropdown to organize rates by:
  - Latest First (default)
  - Oldest First
  - Highest Rate
  - Lowest Rate

### Pagination
- Navigate through pages using the pagination controls
- Adjust the number of items per page (10, 25, 50, 100)
- Use keyboard shortcuts or click navigation buttons

## Data Fields

### Hourly Rate
- **Type**: Number (decimal)
- **Range**: $0.01 - $10,000.00
- **Format**: USD currency with 2 decimal places
- **Validation**: Required, must be positive

### Effective From
- **Type**: Date
- **Format**: YYYY-MM-DD (input), DD-MMM-YYYY (display)
- **Validation**: Required
- **Note**: Future dates are allowed with a warning

## Browser Compatibility

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## Accessibility Features

- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Focus Management**: Clear focus indicators and logical tab order
- **High Contrast**: Supports high contrast mode
- **Reduced Motion**: Respects user's motion preferences
- **Color Accessibility**: WCAG compliant color contrast ratios

## Technical Details

### CSS Architecture
- **CSS Custom Properties**: Consistent theming and easy customization
- **Mobile-First Design**: Responsive breakpoints at 768px and 480px
- **Modern Layout**: CSS Grid for card layout, Flexbox for components
- **Component-Based**: Modular CSS classes for reusability

### JavaScript Architecture
- **ES6+ Features**: Classes, arrow functions, template literals
- **Event-Driven**: Efficient event handling with delegation
- **State Management**: Centralized state in the main class
- **Error Handling**: Comprehensive error handling and user feedback

### Performance Optimizations
- **Efficient Rendering**: Only re-renders necessary components
- **Debounced Search**: Optimized search performance
- **Lazy Loading**: Pagination reduces DOM elements
- **Memory Management**: Proper cleanup of event listeners

## Customization

### Theming
The application uses CSS custom properties for easy theming. Key variables include:
- `--primary-color`: Main brand color
- `--surface-color`: Card and modal backgrounds
- `--text-primary`: Main text color
- `--border-color`: Border and divider color

### Configuration
You can modify these settings in the JavaScript:
- `pageSize`: Default number of items per page
- `maxVisible`: Maximum visible page numbers in pagination
- Toast notification duration and positioning

## Sample Data

The application comes with sample data similar to the original application:
- 18 pre-loaded hourly rates
- Rates ranging from $125.00 to $170.00
- Effective dates from 2017 to 2024
- Demonstrates various scenarios and use cases

## Future Enhancements

Potential improvements for future versions:
- 📊 **Charts and Analytics**: Visual representation of rate trends
- 🔄 **Data Import**: Import rates from CSV or Excel files
- 🌐 **Multi-Currency**: Support for different currencies
- 👥 **Multi-User**: User accounts and data sharing
- 🔒 **Data Encryption**: Enhanced security for sensitive data
- 📱 **PWA Support**: Offline functionality and app installation

## License

This project is open source and available under the MIT License.

## Support

For questions, issues, or feature requests, please refer to the project documentation or create an issue in the project repository.
