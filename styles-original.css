/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette - Modern but professional */
    --primary-color: #007bff;
    --primary-hover: #0056b3;
    --success-color: #28a745;
    --success-hover: #1e7e34;
    --danger-color: #dc3545;
    --danger-hover: #c82333;
    --secondary-color: #6c757d;
    --secondary-hover: #545b62;
    --light-gray: #f8f9fa;
    --border-color: #dee2e6;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #868e96;
    --background: #ffffff;
    --table-stripe: #f8f9fa;
    --table-hover: #e9ecef;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;

    /* Shadows */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

    /* Transitions */
    --transition: 0.15s ease-in-out;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--light-gray);
    color: var(--text-primary);
    line-height: 1.5;
    font-size: 14px;
}

.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background: var(--background);
    min-height: 100vh;
    box-shadow: var(--shadow-sm);
}

/* Header */
.app-header {
    text-align: center;
    padding: var(--spacing-lg) 0;
    border-bottom: 2px solid var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.app-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

/* Toolbar */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--light-gray);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* View Mode Selector */
.view-mode-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.view-mode-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    white-space: nowrap;
}

.view-mode-buttons {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    overflow: hidden;
    background: var(--background);
}

.btn-view-mode {
    padding: var(--spacing-sm);
    border: none;
    background: var(--background);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition);
    min-width: 2.5rem;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid var(--border-color);
}

.btn-view-mode:last-child {
    border-right: none;
}

.btn-view-mode:hover {
    background: var(--table-hover);
    color: var(--text-primary);
}

.btn-view-mode.active {
    background: var(--primary-color);
    color: white;
}

.btn-view-mode:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition);
    text-decoration: none;
    white-space: nowrap;
    min-height: 2.25rem;
    background: none;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: var(--success-hover);
    border-color: var(--success-hover);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: var(--danger-hover);
    border-color: var(--danger-hover);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--secondary-hover);
    border-color: var(--secondary-hover);
}

/* Data Container and View Management */
.data-container {
    margin-bottom: var(--spacing-lg);
}

.view-container {
    display: none;
}

.view-container.active {
    display: block;
}

/* List View (Table) Styles */
.list-view .table-container {
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.data-table th {
    background-color: var(--light-gray);
    color: var(--text-primary);
    font-weight: 600;
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.data-table tbody tr:nth-child(even) {
    background-color: var(--table-stripe);
}

.data-table tbody tr:hover {
    background-color: var(--table-hover);
}

.data-table tbody tr.selected {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Column Widths */
.col-edit, .col-delete {
    width: 60px;
    text-align: center;
}

.col-rate {
    width: 200px;
}

.col-date {
    width: 200px;
}

/* Required Indicator */
.required-indicator {
    color: var(--danger-color);
    font-weight: bold;
    margin-left: var(--spacing-xs);
}

/* Action Buttons in Table */
.btn-action {
    padding: var(--spacing-xs);
    min-height: 1.75rem;
    width: 1.75rem;
    justify-content: center;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
}

.btn-edit {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-edit:hover {
    background-color: var(--primary-hover);
}

.btn-delete {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.btn-delete:hover {
    background-color: var(--danger-hover);
}

/* Checkbox Styles */
.checkbox-cell {
    text-align: center;
}

.checkbox-cell input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-color);
    cursor: pointer;
}

/* Rate Display */
.rate-value {
    font-weight: 500;
    color: var(--text-primary);
}

.date-value {
    color: var(--text-primary);
}

/* Grid View Styles */
.grid-container {
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.grid-header {
    display: grid;
    grid-template-columns: 120px 1fr 1fr;
    background: var(--light-gray);
    border-bottom: 2px solid var(--border-color);
}

.grid-header-cell {
    padding: var(--spacing-md);
    font-weight: 600;
    color: var(--text-primary);
    border-right: 1px solid var(--border-color);
    display: flex;
    align-items: center;
}

.grid-header-cell:last-child {
    border-right: none;
}

.grid-body {
    display: flex;
    flex-direction: column;
}

.grid-row {
    display: grid;
    grid-template-columns: 120px 1fr 1fr;
    border-bottom: 1px solid var(--border-color);
    transition: background-color var(--transition);
}

.grid-row:nth-child(even) {
    background-color: var(--table-stripe);
}

.grid-row:hover {
    background-color: var(--table-hover);
}

.grid-row.selected {
    background-color: rgba(0, 123, 255, 0.1);
}

.grid-cell {
    padding: var(--spacing-md);
    border-right: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    min-height: 3rem;
}

.grid-cell:last-child {
    border-right: none;
}

.grid-cell-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.grid-cell-actions .checkbox-cell {
    margin-right: var(--spacing-sm);
}

/* Card View Styles */
.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-sm);
}

.rate-card {
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition);
    overflow: hidden;
}

.rate-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.rate-card.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.rate-card-header {
    padding: var(--spacing-lg);
    background: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.rate-card-info {
    flex: 1;
}

.rate-card-amount {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.rate-card-currency {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.rate-card-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.rate-card-body {
    padding: var(--spacing-lg);
}

.rate-card-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.rate-card-date i {
    color: var(--text-muted);
    font-size: 1rem;
}

.rate-card-date-value {
    font-weight: 500;
    color: var(--text-primary);
}

.rate-card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--table-stripe);
    border-top: 1px solid var(--border-color);
}

.rate-card-checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.rate-card-checkbox input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-color);
    cursor: pointer;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--light-gray);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-page {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    background: var(--background);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition);
    min-height: 2rem;
    min-width: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-page:hover:not(:disabled) {
    background: var(--table-hover);
    border-color: var(--primary-color);
}

.btn-page:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0 var(--spacing-sm);
}

.page-size-selector select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--background);
    font-size: 0.875rem;
    cursor: pointer;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition);
    padding: var(--spacing-lg);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--background);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all var(--transition);
}

.modal-overlay.active .modal {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--light-gray);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition);
}

.btn-close:hover {
    color: var(--text-primary);
    background: var(--table-hover);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    background: var(--light-gray);
}

.confirm-modal {
    max-width: 400px;
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.form-group label.required::after {
    content: ' *';
    color: var(--danger-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all var(--transition);
    background: var(--background);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.input-group {
    position: relative;
    display: flex;
}

.error-message {
    margin-top: var(--spacing-xs);
    font-size: 0.75rem;
    color: var(--danger-color);
    font-weight: 500;
    min-height: 1rem;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--background);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border-left: 4px solid var(--primary-color);
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    transition: all var(--transition);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--danger-color);
}

.toast.warning {
    border-left-color: #ffc107;
}

.toast-icon {
    font-size: 1.125rem;
    margin-top: 0.125rem;
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--danger-color);
}

.toast.warning .toast-icon {
    color: #ffc107;
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.toast-message {
    font-size: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    font-size: 1rem;
    transition: color var(--transition);
}

.toast-close:hover {
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-container {
        padding: var(--spacing-md);
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .toolbar-section {
        justify-content: center;
    }

    .toolbar .btn {
        justify-content: center;
    }

    .view-mode-selector {
        justify-content: center;
    }

    /* List View Responsive */
    .list-view .table-container {
        overflow-x: auto;
    }

    .data-table {
        min-width: 600px;
    }

    /* Grid View Responsive */
    .grid-header,
    .grid-row {
        grid-template-columns: 100px 1fr 1fr;
    }

    .grid-container {
        overflow-x: auto;
    }

    /* Card View Responsive */
    .cards-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .pagination-container {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }

    .modal {
        margin: var(--spacing-md);
        max-width: none;
    }

    .toast-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
    }

    .toast {
        min-width: auto;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .app-title {
        font-size: 1.5rem;
    }

    .btn {
        font-size: 0.75rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .data-table th,
    .data-table td {
        padding: var(--spacing-sm);
    }

    .pagination-controls .page-info {
        display: none;
    }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Focus Styles for Accessibility */
.btn:focus-visible,
input:focus-visible,
select:focus-visible,
.data-table tbody tr:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    color: var(--text-muted);
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}
